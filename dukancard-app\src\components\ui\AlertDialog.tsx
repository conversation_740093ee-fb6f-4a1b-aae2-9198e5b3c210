import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { X, AlertTriangle, CheckCircle, XCircle, Info, HelpCircle } from 'lucide-react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';

export type AlertType = 'success' | 'error' | 'warning' | 'info' | 'question' | 'custom';

export interface AlertButton {
  text: string;
  onPress: () => void;
  style?: 'default' | 'destructive' | 'cancel';
  loading?: boolean;
}

interface AlertDialogProps {
  visible: boolean;
  type?: AlertType;
  title: string;
  message?: string;
  buttons: AlertButton[];
  onClose?: () => void;
  showCloseButton?: boolean;
  customIcon?: React.ReactNode;
}

const { width: screenWidth } = Dimensions.get('window');

const getIconForType = (type: AlertType, color: string) => {
  const iconSize = 32;
  switch (type) {
    case 'success':
      return <CheckCircle size={iconSize} color="#10B981" />;
    case 'error':
      return <XCircle size={iconSize} color="#EF4444" />;
    case 'warning':
      return <AlertTriangle size={iconSize} color="#F59E0B" />;
    case 'info':
      return <Info size={iconSize} color="#3B82F6" />;
    case 'question':
      return <HelpCircle size={iconSize} color="#8B5CF6" />;
    default:
      return null;
  }
};

const getIconBackgroundColor = (type: AlertType) => {
  switch (type) {
    case 'success':
      return '#10B981' + '20';
    case 'error':
      return '#EF4444' + '20';
    case 'warning':
      return '#F59E0B' + '20';
    case 'info':
      return '#3B82F6' + '20';
    case 'question':
      return '#8B5CF6' + '20';
    default:
      return '#6B7280' + '20';
  }
};

const getButtonColor = (style: AlertButton['style'], isDark: boolean) => {
  switch (style) {
    case 'destructive':
      return '#EF4444';
    case 'cancel':
      return isDark ? '#1F2937' : '#F9FAFB'; // Darker gray for dark mode, lighter for light mode
    default:
      return '#D4AF37'; // Gold color
  }
};

const getButtonTextColor = (style: AlertButton['style'], isDark: boolean) => {
  switch (style) {
    case 'destructive':
      return '#FFFFFF';
    case 'cancel':
      return isDark ? '#D1D5DB' : '#374151'; // Light gray for dark mode, dark gray for light mode
    default:
      return '#000000';
  }
};

export function AlertDialog({
  visible,
  type = 'info',
  title,
  message,
  buttons,
  onClose,
  showCloseButton = true,
  customIcon,
}: AlertDialogProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const secondaryTextColor = isDark ? '#9CA3AF' : '#6B7280';
  const borderColor = isDark ? '#374151' : '#E5E7EB';
  const overlayColor = isDark ? 'rgba(0, 0, 0, 0.8)' : 'rgba(0, 0, 0, 0.5)';

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const icon = customIcon || getIconForType(type, textColor);
  const iconBgColor = getIconBackgroundColor(type);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      
      onRequestClose={handleClose}
    >
      {/* Backdrop */}
      <View style={[styles.backdrop, { backgroundColor: overlayColor }]}>
        <BlurView
          intensity={20}
          style={StyleSheet.absoluteFillObject}
          tint={isDark ? 'dark' : 'light'}
        />
        
        {/* Dialog Container */}
        <View style={styles.dialogContainer}>
          <View style={[
            styles.dialog,
            {
              backgroundColor,
              borderColor,
              shadowColor: isDark ? '#FFFFFF' : '#000000',
            }
          ]}>
            {/* Close Button */}
            {showCloseButton && onClose && (
              <TouchableOpacity
                style={[styles.closeButton, { borderColor: borderColor }]}
                onPress={handleClose}
                activeOpacity={0.7}
              >
                <X size={20} color={secondaryTextColor} />
              </TouchableOpacity>
            )}

            {/* Icon */}
            {icon && (
              <View style={[styles.iconContainer, { backgroundColor: iconBgColor }]}>
                {icon}
              </View>
            )}

            {/* Title */}
            <Text style={[styles.title, { color: textColor }]}>
              {title}
            </Text>

            {/* Message */}
            {message && (
              <Text style={[styles.message, { color: secondaryTextColor }]}>
                {message}
              </Text>
            )}

            {/* Buttons */}
            <View style={[
              styles.buttonContainer,
              buttons.length === 1 && styles.singleButtonContainer
            ]}>
              {buttons.map((button, index) => {
                const buttonBgColor = getButtonColor(button.style, isDark);
                const buttonTextColor = getButtonTextColor(button.style, isDark);
                const isCancel = button.style === 'cancel';

                return (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.button,
                      buttons.length === 1 && styles.singleButton,
                      {
                        backgroundColor: buttonBgColor,
                        borderColor: isCancel ? (isDark ? '#374151' : '#E5E7EB') : buttonBgColor,
                        borderWidth: isCancel ? 1 : 0,
                      },
                      button.loading && styles.buttonDisabled
                    ]}
                    onPress={button.onPress}
                    disabled={button.loading}
                    activeOpacity={0.7}
                  >
                    {button.loading ? (
                      <ActivityIndicator 
                        size="small" 
                        color={isCancel ? textColor : buttonTextColor} 
                      />
                    ) : (
                      <Text style={[styles.buttonText, { color: buttonTextColor }]}>
                        {button.text}
                      </Text>
                    )}
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogContainer: {
    width: screenWidth - 48,
    maxWidth: 400,
  },
  dialog: {
    borderRadius: 20,
    padding: 24,
    borderWidth: 1,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 12,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 32,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  singleButtonContainer: {
    flexDirection: 'column',
  },
  button: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 52,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  singleButton: {
    flex: 0,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
