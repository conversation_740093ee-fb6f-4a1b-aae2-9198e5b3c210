(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/app/(dashboard)/dashboard/business/plan/components/PaymentMethodLimitationsDialog.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/_00e313c2._.js",
  "static/chunks/c293b_dashboard_business_plan_components_PaymentMethodLimitationsDialog_tsx_053ab78e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/app/(dashboard)/dashboard/business/plan/components/PaymentMethodLimitationsDialog.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);