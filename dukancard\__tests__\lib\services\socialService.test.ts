import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { createClient } from '@/utils/supabase/server';
import { subscriptionsService, likesService, reviewsService, getActivityMetrics } from '@/lib/services/socialService';

// Mock the Supabase client
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));

// Mock constants
jest.mock('@/lib/supabase/constants', () => ({
  TABLES: {
    SUBSCRIPTIONS: 'subscriptions',
    BUSINESS_PROFILES: 'business_profiles',
    LIKES: 'likes',
    RATINGS_REVIEWS: 'ratings_reviews',
    CUSTOMER_PROFILES: 'customer_profiles',
  },
  COLUMNS: {
    ID: 'id',
    USER_ID: 'user_id',
    BUSINESS_PROFILE_ID: 'business_profile_id',
    BUSINESS_NAME: 'business_name',
    CREATED_AT: 'created_at',
    RATING: 'rating',
    REVIEW_TEXT: 'review_text',
    NAME: 'name',
    EMAIL: 'email',
    AVATAR_URL: 'avatar_url',
    BUSINESS_SLUG: 'business_slug',
    LOGO_URL: 'logo_url',
    CITY: 'city',
    STATE: 'state',
    PINCODE: 'pincode',
    ADDRESS_LINE: 'address_line',
    UPDATED_AT: 'updated_at',
  },
}));

describe('socialService', () => {
  let mockSupabase: any;

  beforeEach(() => {
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      ilike: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
    };

    (createClient as jest.Mock).mockImplementation(() => mockSupabase);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('subscriptionsService', () => {
    describe('fetchSubscriptions', () => {
      it('should fetch subscriptions with pagination', async () => {
        const mockData = [
          {
            id: '1',
            business_profile_id: 'business1',
            business_profiles: {
              id: 'business1',
              business_name: 'Test Business',
              business_slug: 'test-business',
            },
          },
        ];

        mockSupabase.select.mockResolvedValueOnce({
          data: mockData,
          count: 1,
          error: null,
        });

        const result = await subscriptionsService.fetchSubscriptions(
          'user1',
          1,
          10,
          ''
        );

        expect(result.items).toEqual(mockData);
        expect(result.totalCount).toBe(1);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });

      it('should handle search term', async () => {
        mockSupabase.select.mockResolvedValueOnce({
          data: [],
          count: 0,
          error: null,
        });

        await subscriptionsService.fetchSubscriptions(
          'user1',
          1,
          10,
          'search term'
        );

        expect(mockSupabase.ilike).toHaveBeenCalledWith(
          'business_profiles.business_name',
          '%search term%'
        );
      });

      it('should handle errors', async () => {
        mockSupabase.select.mockResolvedValueOnce({
          data: null,
          count: null,
          error: { message: 'Database error' },
        });

        await expect(
          subscriptionsService.fetchSubscriptions('user1', 1, 10, '')
        ).rejects.toThrow('Database error');
      });
    });

    describe('unsubscribe', () => {
      it('should delete subscription successfully', async () => {
        mockSupabase.delete.mockResolvedValueOnce({
          error: null,
        });

        await subscriptionsService.unsubscribe('subscription1');

        expect(mockSupabase.from).toHaveBeenCalledWith('subscriptions');
        expect(mockSupabase.delete).toHaveBeenCalled();
        expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'subscription1');
      });

      it('should handle delete errors', async () => {
        mockSupabase.delete.mockResolvedValueOnce({
          error: { message: 'Delete failed' },
        });

        await expect(
          subscriptionsService.unsubscribe('subscription1')
        ).rejects.toThrow('Delete failed');
      });
    });
  });

  describe('likesService', () => {
    describe('fetchLikes', () => {
      it('should fetch likes with pagination', async () => {
        const mockCountData = { count: 5, error: null };
        const mockData = [
          {
            id: '1',
            business_profiles: {
              id: 'business1',
              business_name: 'Test Business',
            },
          },
        ];

        // Mock count query
        mockSupabase.select.mockResolvedValueOnce(mockCountData);
        // Mock data query
        mockSupabase.select.mockResolvedValueOnce({
          data: mockData,
          error: null,
        });

        const result = await likesService.fetchLikes(
          'user1',
          1,
          10,
          'newest'
        );

        expect(result.items).toEqual(mockData);
        expect(result.totalCount).toBe(5);
        expect(result.hasMore).toBe(false);
        expect(result.currentPage).toBe(1);
      });
    });

    describe('unlike', () => {
      it('should delete like successfully', async () => {
        mockSupabase.delete.mockResolvedValueOnce({
          error: null,
        });

        await likesService.unlike('like1');

        expect(mockSupabase.from).toHaveBeenCalledWith('likes');
        expect(mockSupabase.delete).toHaveBeenCalled();
        expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'like1');
      });
    });
  });

  describe('reviewsService', () => {
    describe('fetchReviews', () => {
      it('should fetch reviews with pagination and sorting', async () => {
        const mockData = [
          {
            id: '1',
            rating: 5,
            review_text: 'Great service!',
            created_at: '2024-01-01T00:00:00Z',
            business_profiles: {
              id: 'business1',
              business_name: 'Test Business',
            },
          },
        ];

        mockSupabase.select.mockResolvedValueOnce({
          data: mockData,
          count: 1,
          error: null,
        });

        const result = await reviewsService.fetchReviews(
          'user1',
          1,
          10,
          'rating_high'
        );

        expect(result.items).toEqual(mockData);
        expect(result.totalCount).toBe(1);
        expect(mockSupabase.order).toHaveBeenCalledWith('rating', { ascending: false });
      });
    });

    describe('deleteReview', () => {
      it('should delete review successfully', async () => {
        mockSupabase.delete.mockResolvedValueOnce({
          error: null,
        });

        await reviewsService.deleteReview('review1');

        expect(mockSupabase.from).toHaveBeenCalledWith('ratings_reviews');
        expect(mockSupabase.delete).toHaveBeenCalled();
        expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'review1');
      });
    });

    describe('updateReview', () => {
      it('should update review successfully', async () => {
        mockSupabase.update.mockResolvedValueOnce({
          error: null,
        });

        await reviewsService.updateReview(
          'review1',
          4,
          'Updated review text'
        );

        expect(mockSupabase.from).toHaveBeenCalledWith('ratings_reviews');
        expect(mockSupabase.update).toHaveBeenCalledWith({
          rating: 4,
          review_text: 'Updated review text',
          updated_at: expect.any(String),
        });
        expect(mockSupabase.eq).toHaveBeenCalledWith('id', 'review1');
      });
    });
  });

  describe('getActivityMetrics', () => {
    it('should fetch activity metrics for user', async () => {
      // Mock all three queries
      mockSupabase.select
        .mockResolvedValueOnce({ count: 5, error: null }) // likes
        .mockResolvedValueOnce({ count: 3, error: null }) // reviews
        .mockResolvedValueOnce({ count: 2, error: null }); // subscriptions

      const result = await getActivityMetrics('user1');

      expect(result).toEqual({
        likesCount: 5,
        reviewCount: 3,
        subscriptionCount: 2,
        lastUpdated: expect.any(String),
      });
    });

    it('should handle errors in activity metrics', async () => {
      mockSupabase.select.mockResolvedValueOnce({
        count: null,
        error: { message: 'Database error' },
      });

      const result = await getActivityMetrics('user1');
      expect(result).toBeNull();
    });
  });
});
