import { useColorScheme } from "@/src/hooks/useColorScheme";
import React, { useRef, useEffect, useState } from "react";
import { StatusBar, StyleSheet, Animated } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Header } from "../ui/Header";

interface DashboardLayoutProps {
  children: React.ReactNode;
  userName?: string | null;
  businessName?: string | null;
  avatarUrl?: string | null;
  showNotifications?: boolean;
  onProfilePress?: () => void;
  onNotificationPress?: () => void; // Optional override for notification press
  fullWidth?: boolean; // New prop to remove horizontal padding for full-width content
  onScroll?: (event: any) => void; // Scroll event handler for collapsible header
  scrollEventThrottle?: number; // Throttle for scroll events
  hideHeader?: boolean; // New prop to hide the header completely
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  userName,
  businessName,
  avatarUrl,
  showNotifications = true,
  onProfilePress,
  onNotificationPress,
  fullWidth = false,
  onScroll,
  scrollEventThrottle = 16,
  hideHeader = false,
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const statusBarStyle = isDark ? "light-content" : "dark-content";

  // Animation values for collapsible header
  const headerTranslateY = useRef(new Animated.Value(0)).current; // For translateY (native)
  const contentPaddingTop = useRef(new Animated.Value(60)).current; // For content padding (non-native)
  const lastScrollY = useRef(0);
  const scrollDirection = useRef<"up" | "down">("up");
  const [headerHeight, setHeaderHeight] = useState(52); // More realistic default height

  // Handle header layout to get dynamic height
  const handleHeaderLayout = (event: any) => {
    const { height } = event.nativeEvent.layout;
    setHeaderHeight(height);
    // Update content padding to match header height
    contentPaddingTop.setValue(height + 8);
  };

  // Handle scroll events for collapsible header
  const handleScroll = (event: any) => {
    if (onScroll) {
      onScroll(event);
    }

    const currentScrollY = event.nativeEvent.contentOffset.y;
    const scrollDiff = currentScrollY - lastScrollY.current;

    // Only trigger animation if scroll difference is significant enough
    if (Math.abs(scrollDiff) < 5) return;

    // Determine scroll direction
    if (
      scrollDiff > 0 &&
      scrollDirection.current !== "down" &&
      currentScrollY > 50
    ) {
      // Scrolling down - hide header (only after scrolling past 50px)
      scrollDirection.current = "down";
      Animated.parallel([
        Animated.timing(headerTranslateY, {
          toValue: -headerHeight, // Hide header using dynamic height
          duration: 250,
          useNativeDriver: true, // Use native driver for better performance
        }),
        Animated.timing(contentPaddingTop, {
          toValue: 8, // Reduce content padding when header is hidden
          duration: 250,
          useNativeDriver: false, // Can't use native driver for padding
        }),
      ]).start();
    } else if (scrollDiff < 0 && scrollDirection.current !== "up") {
      // Scrolling up - show header
      scrollDirection.current = "up";
      Animated.parallel([
        Animated.timing(headerTranslateY, {
          toValue: 0, // Show header
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(contentPaddingTop, {
          toValue: headerHeight + 8, // Restore full padding when header is shown
          duration: 250,
          useNativeDriver: false,
        }),
      ]).start();
    }

    lastScrollY.current = currentScrollY;
  };

  // Clone children and pass scroll handler if it's a scrollable component
  const childrenWithProps = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child as React.ReactElement<any>, {
        onScroll: handleScroll,
        scrollEventThrottle,
      });
    }
    return child;
  });

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <StatusBar
        barStyle={statusBarStyle}
        backgroundColor={backgroundColor}
        
      />

      {/* Main Content - Full height */}
      <Animated.View
        style={[
          fullWidth ? styles.contentFullWidth : styles.content,
          {
            backgroundColor,
            paddingTop: hideHeader ? 0 : contentPaddingTop, // No padding when header is hidden
          },
        ]}
      >
        {childrenWithProps}
      </Animated.View>

      {/* Animated Header - Absolute positioned - Only show if hideHeader is false */}
      {!hideHeader && (
        <Animated.View
          style={[
            styles.headerContainer,
            {
              transform: [{ translateY: headerTranslateY }],
              backgroundColor,
            },
          ]}
          onLayout={handleHeaderLayout}
        >
          <Header
            userName={userName}
            businessName={businessName}
            avatarUrl={avatarUrl}
            showNotifications={showNotifications}
            onProfilePress={onProfilePress}
            onNotificationPress={onNotificationPress}
          />
        </Animated.View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    elevation: 10, // For Android shadow
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16, // Add bottom padding for bottom navigation spacing
    // paddingTop is set dynamically in the component
  },
  contentFullWidth: {
    flex: 1,
    paddingBottom: 16, // Add bottom padding for bottom navigation spacing
    // paddingTop is set dynamically in the component
    // No horizontal padding for full-width content like feeds
  },
});

export default DashboardLayout;
