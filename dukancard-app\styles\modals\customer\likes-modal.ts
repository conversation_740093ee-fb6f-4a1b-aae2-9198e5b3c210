import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { responsiveFontSize } from "@/lib/theme/colors";

export const createLikesModalStyles = (theme: ReturnType<typeof useTheme>) => {
  const { colors, spacing, borderRadius, typography, isDark } = theme;

  return StyleSheet.create({
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
    },
    closeButton: {
      padding: spacing.xs,
    },
    contentContainer: {
      flex: 1,
    },
    searchContainer: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
    },
    searchInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.card,
      borderRadius: borderRadius.lg,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    searchInput: {
      flex: 1,
      fontSize: typography.fontSize.base,
      color: colors.foreground,
      marginLeft: spacing.sm,
    },
    searchIcon: {
      opacity: 0.6,
    },
    listContainer: {
      flex: 1,
      paddingHorizontal: spacing.md,
    },
    // Empty state
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: spacing.lg,
    },
    emptyIcon: {
      marginBottom: spacing.md,
    },
    emptyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
      textAlign: "center",
      marginBottom: spacing.sm,
    },
    emptyText: {
      fontSize: typography.fontSize.base,
      color: colors.mutedForeground,
      textAlign: "center",
      lineHeight: typography.lineHeight.relaxed,
    },

    // Loading states
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: spacing.xl,
    },
    footerLoadingContainer: {
      paddingVertical: spacing.lg,
      alignItems: "center",
    },
    toggleContainer: {
      flexDirection: "row",
      marginHorizontal: theme.spacing.md,
      marginBottom: theme.spacing.md,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.xs,
    },
    toggleButton: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
      alignItems: "center",
    },
    toggleButtonActive: {
      backgroundColor: theme.colors.primary,
    },
    toggleButtonText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "500",
      color: theme.colors.textSecondary,
    },
    toggleButtonTextActive: {
      color: theme.colors.primaryForeground,
    },
  });
};
