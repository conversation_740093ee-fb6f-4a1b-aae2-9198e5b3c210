"use client";

import * as React from "react";
import Link from "next/link";
import { SidebarLink } from "./SidebarLink";
import {
  Sidebar,
  <PERSON>barContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarGroup,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Badge } from "@/components/ui/badge";
import { NavBusinessUser } from "./NavBusinessUser";
import { ChevronRight } from "lucide-react";
// Import the icon map from NavBusinessMain
import { iconMap } from "./NavBusinessMain";
import { realtimeService } from "@/lib/services/realtimeService";
import { createClient } from "@/utils/supabase/client";

// Define types for navigation items
interface NavItem {
  title: string;
  icon?: string;
  url?: string;
  badge?: string;
  badgeVariant?:
    | "default"
    | "secondary"
    | "destructive"
    | "outline"
    | "upgrade";
}

interface NavSection {
  title: string;
  icon?: string;
  url?: string;
  items: NavItem[];
}

// Define props if necessary, e.g., business data
interface BusinessAppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  businessName: string | null;
  logoUrl: string | null;
  memberName: string | null;
  userPlan: string | null;
}

export function BusinessAppSidebar({
  businessName: propBusinessName,
  logoUrl: propLogoUrl,
  memberName: propMemberName,
  userPlan: propUserPlan,
  ...props
}: BusinessAppSidebarProps) {
  // Use props data directly
  const businessName = propBusinessName;
  const logoUrl = propLogoUrl;
  const memberName = propMemberName;
  const userPlan = propUserPlan; // Keep using prop for plan as it comes from subscription data

  // Prepare data structure for nav components
  const userData = {
    name: memberName,
    // email: memberEmail, // Assuming email might be available later
    avatar: logoUrl, // Use logoUrl for avatar for now
  };

  // Define main navigation items for business dashboard with collapsible sections
  const navData: NavSection[] = [
    {
      title: "Feed",
      icon: "LayoutList",
      url: "/dashboard/business",
      items: [],
    },
    {
      title: "Overview",
      icon: "LayoutDashboard",
      url: "/dashboard/business/overview",
      items: [],
    },
    {
      title: "Business Management",
      icon: "Store",
      items: [
        {
          title: "Manage Card",
          icon: "CreditCard",
          url: "/dashboard/business/card",
        },
        {
          title: "Products & Services",
          icon: "Package",
          url: "/dashboard/business/products",
        },
        {
          title: "Gallery",
          icon: "Image",
          url: "/dashboard/business/gallery",
          badge:
            userPlan === "free"
              ? "1 Photo"
              : userPlan === "basic"
              ? "3 Photos"
              : undefined,
          badgeVariant: "secondary" as const,
        },
      ],
    },
    {
      title: "Insights",
      icon: "BarChart3",
      items: [
        {
          title: "Analytics",
          icon: "BarChart3",
          url: "/dashboard/business/analytics",
          badge: userPlan === "free" ? "Basic+" : undefined,
          badgeVariant: "upgrade" as const,
        },
      ],
    },
    {
      title: "Social",
      icon: "Users",
      items: [
        {
          title: "Likes",
          icon: "Heart",
          url: "/dashboard/business/likes",
        },
        {
          title: "Subscriptions",
          icon: "Users",
          url: "/dashboard/business/subscriptions",
        },
        {
          title: "Reviews",
          icon: "Star",
          url: "/dashboard/business/reviews",
        },
      ],
    },
    {
      title: "Account",
      icon: "User",
      items: [
        {
          title: "Manage Plan",
          icon: "WalletCards",
          url: "/dashboard/business/plan",
        },
        {
          title: "Settings",
          icon: "Settings",
          url: "/dashboard/business/settings",
        },
      ],
    },
  ];

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="border-b border-border/50">
        <div className="flex items-center px-2 py-4">
          <Link
            href="/?view=home"
            className="flex flex-col group transition-all duration-200 hover:opacity-80"
          >
            <span className="font-bold text-lg text-[var(--brand-gold)]">
              Dukan<span className="text-foreground">card</span>
            </span>
            <span className="text-xs text-muted-foreground">
              Business Portal
            </span>
          </Link>
        </div>
      </SidebarHeader>
      <SidebarContent className="px-2">
        <SidebarGroup>
          <SidebarMenu className="space-y-1">
            {navData.map((section, index) => {
              // For Feed and Overview (first two items), render as direct links
              if (index === 0 || index === 1) {
                return (
                  <SidebarMenuItem key={section.title}>
                    <SidebarMenuButton
                      asChild
                      tooltip={section.title}
                      className="h-10 rounded-lg"
                    >
                      <SidebarLink
                        href={section.url || "#"}
                        className="flex items-center gap-3"
                      >
                        {section.icon &&
                          iconMap[section.icon] &&
                          React.createElement(iconMap[section.icon], {
                            className: "h-4 w-4 text-muted-foreground",
                          })}
                        <span className="font-medium">{section.title}</span>
                      </SidebarLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              }

              // For other sections, render as collapsible
              return (
                <Collapsible
                  key={section.title}
                  defaultOpen={index === 2} // Open the Business Management section by default
                  className="group/collapsible"
                >
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton className="h-10 rounded-lg">
                        {section.icon &&
                          iconMap[section.icon] &&
                          React.createElement(iconMap[section.icon], {
                            className: "h-4 w-4 text-muted-foreground",
                          })}
                        <span className="font-medium">{section.title}</span>
                        <ChevronRight className="ml-auto h-4 w-4 text-muted-foreground group-data-[state=open]/collapsible:rotate-90" />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="transition-all duration-200">
                      <SidebarMenuSub className="ml-4 mt-1 space-y-1 border-l border-border/30 pl-4">
                        {section.items.map((item) => (
                          <SidebarMenuSubItem key={item.title}>
                            <SidebarMenuSubButton
                              asChild
                              className="h-9 rounded-md"
                            >
                              <SidebarLink
                                href={item.url || "#"}
                                className="flex items-center gap-3"
                              >
                                {item.icon &&
                                  iconMap[item.icon] &&
                                  React.createElement(iconMap[item.icon], {
                                    className:
                                      "h-3.5 w-3.5 text-muted-foreground",
                                  })}
                                <span className="text-sm font-medium">
                                  {item.title}
                                </span>
                                {item.badge && (
                                  <Badge
                                    variant={
                                      item.badgeVariant === "upgrade"
                                        ? "default"
                                        : item.badgeVariant || "default"
                                    }
                                    className={`ml-auto text-xs ${
                                      item.badgeVariant === "upgrade" &&
                                      "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300"
                                    }`}
                                  >
                                    {item.badge}
                                  </Badge>
                                )}
                              </SidebarLink>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="border-t border-border/50 p-2">
        <NavBusinessUser user={userData} businessName={businessName} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
