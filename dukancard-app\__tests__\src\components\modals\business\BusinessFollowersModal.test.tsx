import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import { jest } from '@jest/globals';
import BusinessFollowersModal from '@/src/components/modals/business/BusinessFollowersModal';
import { businessSocialService } from '@/backend/supabase/services/business/businessSocialService';

// Mock the business social service
jest.mock('@/backend/supabase/services/business/businessSocialService', () => ({
  businessSocialService: {
    fetchBusinessFollowers: jest.fn(),
    fetchBusinessFollowing: jest.fn(),
  },
}));

// Mock the customer social service
jest.mock('@/backend/supabase/services/posts/socialService', () => ({
  socialService: {
    subscriptionsService: {
      fetchSubscriptions: jest.fn(),
    },
  },
}));

// Mock the theme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      background: '#ffffff',
      foreground: '#000000',
      primary: '#007AFF',
      border: '#e5e5e5',
    },
  }),
}));

// Mock the auth context
jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'user123' },
  }),
}));

// Mock the skeleton component
jest.mock('@/src/components/skeletons/modals/FollowingModalSkeleton', () => {
  return function MockFollowingModalSkeleton() {
    return <div testID="following-modal-skeleton">Loading...</div>;
  };
});

// Mock the followers list components
jest.mock('@/src/components/modals/customer/components/SubscriptionsList', () => {
  return function MockSubscriptionsList({ onSubscriptionCountChange }: any) {
    React.useEffect(() => {
      onSubscriptionCountChange?.(8);
    }, [onSubscriptionCountChange]);
    return <div testID="subscriptions-list">Customer Subscriptions List</div>;
  };
});

jest.mock('@/src/components/modals/business/components/BusinessFollowersList', () => {
  return function MockBusinessFollowersList({ onFollowerCountChange }: any) {
    React.useEffect(() => {
      onFollowerCountChange?.(12);
    }, [onFollowerCountChange]);
    return <div testID="business-followers-list">Business Followers List</div>;
  };
});

describe('BusinessFollowersModal', () => {
  const defaultProps = {
    visible: true,
    onClose: jest.fn(),
    businessId: 'business123',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render modal when visible', () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    expect(screen.getByText('Followers')).toBeTruthy();
    expect(screen.getByText('Followers')).toBeTruthy();
    expect(screen.getByText('Following')).toBeTruthy();
  });

  it('should not render modal when not visible', () => {
    render(<BusinessFollowersModal {...defaultProps} visible={false} />);
    
    expect(screen.queryByText('Followers')).toBeNull();
  });

  it('should call onClose when close button is pressed', () => {
    const onCloseMock = jest.fn();
    render(<BusinessFollowersModal {...defaultProps} onClose={onCloseMock} />);
    
    const closeButton = screen.getByTestId('close-button');
    fireEvent.press(closeButton);
    
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it('should switch between tabs', () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    // Initially should show "Followers" tab
    expect(screen.getByTestId('business-followers-list')).toBeTruthy();
    
    // Switch to "Following" tab
    const followingTab = screen.getByText('Following');
    fireEvent.press(followingTab);
    
    expect(screen.getByTestId('subscriptions-list')).toBeTruthy();
  });

  it('should update follower count when tab content changes', async () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    // Wait for the count to be updated
    await waitFor(() => {
      expect(screen.getByText('12')).toBeTruthy(); // Count from BusinessFollowersList
    });
    
    // Switch to "Following" tab
    const followingTab = screen.getByText('Following');
    fireEvent.press(followingTab);
    
    await waitFor(() => {
      expect(screen.getByText('8')).toBeTruthy(); // Count from SubscriptionsList
    });
  });

  it('should handle search functionality', () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search followers...');
    fireEvent.changeText(searchInput, 'test search');
    
    expect(searchInput.props.value).toBe('test search');
  });

  it('should handle sort functionality', () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    const sortButton = screen.getByTestId('sort-button');
    fireEvent.press(sortButton);
    
    // Should open sort bottom sheet
    expect(screen.getByTestId('sort-bottom-sheet')).toBeTruthy();
  });

  it('should apply search debouncing', async () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search followers...');
    
    // Type multiple characters quickly
    fireEvent.changeText(searchInput, 'j');
    fireEvent.changeText(searchInput, 'jo');
    fireEvent.changeText(searchInput, 'john');
    
    // Should debounce the search
    await waitFor(() => {
      expect(searchInput.props.value).toBe('john');
    }, { timeout: 1000 });
  });

  it('should handle empty state', () => {
    // Mock empty response
    jest.mocked(businessSocialService.fetchBusinessFollowers).mockResolvedValue({
      items: [],
      totalCount: 0,
      hasMore: false,
      currentPage: 1,
    });

    render(<BusinessFollowersModal {...defaultProps} />);
    
    // Should show empty state message
    expect(screen.getByText(/No followers yet/)).toBeTruthy();
  });

  it('should handle loading state', () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    // Should show skeleton while loading
    expect(screen.getByTestId('following-modal-skeleton')).toBeTruthy();
  });

  it('should handle error state', async () => {
    // Mock error response
    jest.mocked(businessSocialService.fetchBusinessFollowers).mockRejectedValue(
      new Error('Failed to fetch followers')
    );

    render(<BusinessFollowersModal {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText(/Error loading followers/)).toBeTruthy();
    });
  });

  it('should maintain scroll position when switching tabs', () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    // Switch between tabs multiple times
    const followingTab = screen.getByText('Following');
    const followersTab = screen.getByText('Followers');
    
    fireEvent.press(followingTab);
    fireEvent.press(followersTab);
    fireEvent.press(followingTab);
    
    // Should maintain proper state
    expect(screen.getByTestId('subscriptions-list')).toBeTruthy();
  });

  it('should handle refresh functionality', () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    const refreshControl = screen.getByTestId('refresh-control');
    fireEvent(refreshControl, 'refresh');
    
    // Should trigger refresh
    expect(businessSocialService.fetchBusinessFollowers).toHaveBeenCalled();
  });

  it('should show correct tab labels', () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    expect(screen.getByText('Followers')).toBeTruthy();
    expect(screen.getByText('Following')).toBeTruthy();
  });

  it('should handle tab switching with proper state management', () => {
    render(<BusinessFollowersModal {...defaultProps} />);
    
    // Check initial state
    expect(screen.getByTestId('business-followers-list')).toBeTruthy();
    
    // Switch to following
    fireEvent.press(screen.getByText('Following'));
    expect(screen.getByTestId('subscriptions-list')).toBeTruthy();
    
    // Switch back to followers
    fireEvent.press(screen.getByText('Followers'));
    expect(screen.getByTestId('business-followers-list')).toBeTruthy();
  });
});
