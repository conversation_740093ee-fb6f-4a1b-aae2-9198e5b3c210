import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createBusinessReviewsModalStyles = (theme: ReturnType<typeof useTheme>) => {
  const { colors, spacing, borderRadius, typography, isDark } = theme;

  return StyleSheet.create({
    // Modal container
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },

    // Header
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
    },
    headerSubtitle: {
      fontSize: typography.fontSize.sm,
      color: colors.mutedForeground,
      marginTop: spacing.xs,
    },
    closeButton: {
      padding: spacing.xs,
    },

    // Content container
    contentContainer: {
      flex: 1,
    },

    // Toggle container
    toggleContainer: {
      flexDirection: "row",
      marginHorizontal: spacing.md,
      marginTop: spacing.md,
      marginBottom: spacing.sm,
      backgroundColor: colors.card,
      borderRadius: borderRadius.md,
      padding: spacing.xs,
    },
    toggleButton: {
      flex: 1,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borderRadius.sm,
      alignItems: "center",
    },
    toggleButtonActive: {
      backgroundColor: colors.primary,
    },
    toggleButtonInactive: {
      backgroundColor: "transparent",
    },
    toggleButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: "600",
    },
    toggleButtonTextActive: {
      color: colors.primaryForeground,
    },
    toggleButtonTextInactive: {
      color: colors.mutedForeground,
    },

    // Search and sort container
    searchSortContainer: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
    },
    searchInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.card,
      borderRadius: borderRadius.lg,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: spacing.sm,
    },
    searchInput: {
      flex: 1,
      fontSize: typography.fontSize.base,
      color: colors.foreground,
      marginLeft: spacing.sm,
    },
    searchIcon: {
      opacity: 0.6,
    },
    sortContainer: {
      flexDirection: "row",
      justifyContent: "flex-end",
    },
    sortButton: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      backgroundColor: colors.card,
      borderRadius: borderRadius.md,
      borderWidth: 1,
      borderColor: colors.border,
    },
    sortButtonText: {
      fontSize: typography.fontSize.sm,
      color: colors.foreground,
      marginRight: spacing.xs,
    },

    // List container
    listContainer: {
      flex: 1,
      paddingHorizontal: spacing.md,
    },

    // Review card styles
    reviewCard: {
      backgroundColor: colors.card,
      borderRadius: borderRadius.lg,
      marginBottom: spacing.md,
      borderWidth: 1,
      borderColor: colors.border,
      overflow: "hidden",
    },
    reviewHeader: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    reviewerInfo: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    reviewerAvatar: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: colors.muted,
      justifyContent: "center",
      alignItems: "center",
      marginRight: spacing.md,
    },
    reviewerAvatarImage: {
      width: 44,
      height: 44,
      borderRadius: 22,
    },
    reviewerAvatarText: {
      fontSize: typography.fontSize.base,
      fontWeight: "600",
      color: colors.mutedForeground,
    },
    reviewerDetails: {
      flex: 1,
    },
    reviewerName: {
      fontSize: typography.fontSize.base,
      fontWeight: "600",
      color: colors.foreground,
      marginBottom: spacing.xs,
    },
    reviewDate: {
      fontSize: typography.fontSize.sm,
      color: colors.mutedForeground,
    },
    ratingContainer: {
      alignItems: "flex-end",
    },
    starsContainer: {
      flexDirection: "row",
      alignItems: "center",
    },
    reviewContent: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
    },
    reviewText: {
      fontSize: typography.fontSize.base,
      color: colors.foreground,
      lineHeight: typography.lineHeight.relaxed,
    },

    // Empty state
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: spacing.lg,
    },
    emptyIcon: {
      marginBottom: spacing.md,
    },
    emptyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
      textAlign: "center",
      marginBottom: spacing.sm,
    },
    emptyText: {
      fontSize: typography.fontSize.base,
      color: colors.mutedForeground,
      textAlign: "center",
      lineHeight: typography.lineHeight.relaxed,
    },

    // Loading states
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: spacing.xl,
    },
    footerLoadingContainer: {
      paddingVertical: spacing.lg,
      alignItems: "center",
    },

    // Error state
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: spacing.lg,
    },
    errorText: {
      fontSize: typography.fontSize.base,
      color: colors.destructive,
      textAlign: "center",
      marginBottom: spacing.md,
    },
    retryButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
    },
    retryButtonText: {
      color: colors.primaryForeground,
      fontSize: typography.fontSize.sm,
      fontWeight: "600",
    },
  });
};
