import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  Image,
  RefreshControl,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import {
  fetchBusinessLikesReceived,
  BusinessLikeReceived,
} from "@/backend/supabase/services/business/businessSocialService";
import { createBusinessLikesModalStyles } from "@/styles/modals/business/business-likes-modal";
import { BusinessLikesModalSkeleton } from "@/src/components/skeletons/modals/BusinessLikesModalSkeleton";

// Define interfaces for the expected data structure
interface BusinessProfileDataForLike {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface CustomerProfileDataForLike {
  id: string;
  name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface BusinessLikesListProps {
  businessId: string;
  searchTerm: string;
}

export default function BusinessLikesList({
  businessId,
  searchTerm,
}: BusinessLikesListProps) {
  const theme = useTheme();
  const styles = createBusinessLikesModalStyles(theme);
  const [likes, setLikes] = useState<BusinessLikeReceived[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchLikes = useCallback(
    async (isRefreshing = false) => {
      if ((loading && !isRefreshing) || (loadingMore && !isRefreshing)) return;

      if (isRefreshing) {
        setRefreshing(true);
        setPage(1);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      try {
        const currentPage = isRefreshing ? 1 : page;
        const result = await fetchBusinessLikesReceived(
          businessId,
          currentPage,
          10
        );

        if (isRefreshing || currentPage === 1) {
          setLikes(result.items);
        } else {
          setLikes((prev) => [...prev, ...result.items]);
        }
        setHasMore(result.hasMore);
      } catch (error) {
        console.error("Failed to fetch likes:", error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
        setRefreshing(false);
      }
    },
    [businessId, page, loading, loadingMore]
  );

  useEffect(() => {
    setPage(1);
    setLikes([]);
    setHasMore(true);
    fetchLikes(true);
  }, [businessId, searchTerm]);

  useEffect(() => {
    if (page > 1) {
      fetchLikes();
    }
  }, [page]);



  const renderItem = ({ item }: { item: BusinessLikeReceived }) => {
    const profile =
      item.profile_type === "business"
        ? item.business_profiles
        : item.customer_profiles;
    const name =
      item.profile_type === "business"
        ? (profile as BusinessProfileDataForLike)?.business_name
        : (profile as CustomerProfileDataForLike)?.name;
    const avatarUrl =
      item.profile_type === "business"
        ? (profile as any)?.logo_url
        : (profile as any)?.avatar_url;

    const location = item.profile_type === "business"
      ? `${(profile as BusinessProfileDataForLike)?.city || ''}, ${(profile as BusinessProfileDataForLike)?.state || ''}`.replace(/^,\s*|,\s*$/g, '')
      : '';

    return (
      <View style={styles.likeCard}>
        <View style={styles.likeCardAvatar}>
          {avatarUrl ? (
            <Image
              source={{ uri: avatarUrl }}
              style={styles.likeCardAvatarImage}
            />
          ) : (
            <Text style={styles.likeCardAvatarText}>
              {name?.charAt(0).toUpperCase() || '?'}
            </Text>
          )}
        </View>
        <View style={styles.likeCardContent}>
          <Text style={styles.likeCardName}>{name || 'Unknown'}</Text>
          <Text style={styles.likeCardType}>
            {item.profile_type === "business" ? "Business" : "Customer"}
          </Text>
          {location && (
            <Text style={styles.likeCardLocation}>{location}</Text>
          )}
        </View>
      </View>
    );
  };

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footerLoadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  };

  const handleRefresh = () => {
    fetchLikes(true);
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore && !loading) {
      setPage((prevPage) => prevPage + 1);
    }
  };

  // Show skeleton on initial load
  if (loading && page === 1) {
    return <BusinessLikesModalSkeleton />;
  }

  // Show empty state
  if (!loading && likes.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>No Likes Yet</Text>
        <Text style={styles.emptyText}>
          When customers and businesses like your business, they'll appear here.
        </Text>
      </View>
    );
  }

  return (
    <FlatList
      data={likes}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 20 }}
    />
  );
}
