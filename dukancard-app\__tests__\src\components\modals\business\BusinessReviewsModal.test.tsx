import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import { jest } from '@jest/globals';
import BusinessReviewsModal from '@/src/components/modals/business/BusinessReviewsModal';
import { businessSocialService } from '@/backend/supabase/services/business/businessSocialService';

// Mock the business social service
jest.mock('@/backend/supabase/services/business/businessSocialService', () => ({
  businessSocialService: {
    fetchBusinessReviewsReceived: jest.fn(),
  },
}));

// Mock the customer social service
jest.mock('@/backend/supabase/services/posts/socialService', () => ({
  socialService: {
    reviewsService: {
      fetchReviews: jest.fn(),
    },
  },
}));

// Mock the theme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      background: '#ffffff',
      foreground: '#000000',
      primary: '#007AFF',
      border: '#e5e5e5',
    },
  }),
}));

// Mock the auth context
jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'user123' },
  }),
}));

// Mock the skeleton component
jest.mock('@/src/components/skeletons/modals/ReviewsModalSkeleton', () => {
  return function MockReviewsModalSkeleton() {
    return <div testID="reviews-modal-skeleton">Loading...</div>;
  };
});

// Mock the reviews list components
jest.mock('@/src/components/modals/customer/components/ReviewsList', () => {
  return function MockReviewsList({ onReviewCountChange }: any) {
    React.useEffect(() => {
      onReviewCountChange?.(7);
    }, [onReviewCountChange]);
    return <div testID="reviews-list">Customer Reviews List</div>;
  };
});

jest.mock('@/src/components/modals/business/components/BusinessReviewsList', () => {
  return function MockBusinessReviewsList({ onReviewCountChange }: any) {
    React.useEffect(() => {
      onReviewCountChange?.(15);
    }, [onReviewCountChange]);
    return <div testID="business-reviews-list">Business Reviews List</div>;
  };
});

describe('BusinessReviewsModal', () => {
  const defaultProps = {
    visible: true,
    onClose: jest.fn(),
    businessId: 'business123',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render modal when visible', () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    expect(screen.getByText('Reviews')).toBeTruthy();
    expect(screen.getByText('Received')).toBeTruthy();
    expect(screen.getByText('Given')).toBeTruthy();
  });

  it('should not render modal when not visible', () => {
    render(<BusinessReviewsModal {...defaultProps} visible={false} />);
    
    expect(screen.queryByText('Reviews')).toBeNull();
  });

  it('should call onClose when close button is pressed', () => {
    const onCloseMock = jest.fn();
    render(<BusinessReviewsModal {...defaultProps} onClose={onCloseMock} />);
    
    const closeButton = screen.getByTestId('close-button');
    fireEvent.press(closeButton);
    
    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it('should switch between tabs', () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    // Initially should show "Received" tab
    expect(screen.getByTestId('business-reviews-list')).toBeTruthy();
    
    // Switch to "Given" tab
    const givenTab = screen.getByText('Given');
    fireEvent.press(givenTab);
    
    expect(screen.getByTestId('reviews-list')).toBeTruthy();
  });

  it('should update review count when tab content changes', async () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    // Wait for the count to be updated
    await waitFor(() => {
      expect(screen.getByText('15')).toBeTruthy(); // Count from BusinessReviewsList
    });
    
    // Switch to "Given" tab
    const givenTab = screen.getByText('Given');
    fireEvent.press(givenTab);
    
    await waitFor(() => {
      expect(screen.getByText('7')).toBeTruthy(); // Count from ReviewsList
    });
  });

  it('should handle search functionality', () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search reviews...');
    fireEvent.changeText(searchInput, 'excellent service');
    
    expect(searchInput.props.value).toBe('excellent service');
  });

  it('should handle sort functionality', () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    const sortButton = screen.getByTestId('sort-button');
    fireEvent.press(sortButton);
    
    // Should open sort bottom sheet
    expect(screen.getByTestId('sort-bottom-sheet')).toBeTruthy();
  });

  it('should apply search debouncing', async () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search reviews...');
    
    // Type multiple characters quickly
    fireEvent.changeText(searchInput, 'g');
    fireEvent.changeText(searchInput, 'gr');
    fireEvent.changeText(searchInput, 'great');
    
    // Should debounce the search
    await waitFor(() => {
      expect(searchInput.props.value).toBe('great');
    }, { timeout: 1000 });
  });

  it('should handle empty state', () => {
    // Mock empty response
    jest.mocked(businessSocialService.fetchBusinessReviewsReceived).mockResolvedValue({
      items: [],
      totalCount: 0,
      hasMore: false,
      currentPage: 1,
    });

    render(<BusinessReviewsModal {...defaultProps} />);
    
    // Should show empty state message
    expect(screen.getByText(/No reviews received yet/)).toBeTruthy();
  });

  it('should handle loading state', () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    // Should show skeleton while loading
    expect(screen.getByTestId('reviews-modal-skeleton')).toBeTruthy();
  });

  it('should handle error state', async () => {
    // Mock error response
    jest.mocked(businessSocialService.fetchBusinessReviewsReceived).mockRejectedValue(
      new Error('Failed to fetch reviews')
    );

    render(<BusinessReviewsModal {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText(/Error loading reviews/)).toBeTruthy();
    });
  });

  it('should maintain scroll position when switching tabs', () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    // Switch between tabs multiple times
    const givenTab = screen.getByText('Given');
    const receivedTab = screen.getByText('Received');
    
    fireEvent.press(givenTab);
    fireEvent.press(receivedTab);
    fireEvent.press(givenTab);
    
    // Should maintain proper state
    expect(screen.getByTestId('reviews-list')).toBeTruthy();
  });

  it('should handle refresh functionality', () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    const refreshControl = screen.getByTestId('refresh-control');
    fireEvent(refreshControl, 'refresh');
    
    // Should trigger refresh
    expect(businessSocialService.fetchBusinessReviewsReceived).toHaveBeenCalled();
  });

  it('should handle different sort options', () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    const sortButton = screen.getByTestId('sort-button');
    fireEvent.press(sortButton);
    
    // Should show sort options
    expect(screen.getByText('Newest')).toBeTruthy();
    expect(screen.getByText('Oldest')).toBeTruthy();
    expect(screen.getByText('Highest Rating')).toBeTruthy();
    expect(screen.getByText('Lowest Rating')).toBeTruthy();
  });

  it('should apply sort selection', () => {
    render(<BusinessReviewsModal {...defaultProps} />);
    
    const sortButton = screen.getByTestId('sort-button');
    fireEvent.press(sortButton);
    
    // Select highest rating sort
    const highestRatingOption = screen.getByText('Highest Rating');
    fireEvent.press(highestRatingOption);
    
    // Should apply the sort
    expect(businessSocialService.fetchBusinessReviewsReceived).toHaveBeenCalledWith(
      'business123',
      1,
      10,
      'rating_high'
    );
  });
});
