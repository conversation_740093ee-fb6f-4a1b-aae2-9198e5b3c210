import React, { useState } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from "react-native";
import { X, Search } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createBusinessLikesModalStyles } from "@/styles/modals/business/business-likes-modal";
import BusinessLikesList from "./components/BusinessLikesList";
import LikesList from "../customer/components/LikesList";

interface BusinessLikesModalProps {
  visible: boolean;
  onClose: () => void;
  businessId: string;
}

export default function BusinessLikesModal({
  visible,
  onClose,
  businessId,
}: BusinessLikesModalProps) {
  const theme = useTheme();
  const styles = createBusinessLikesModalStyles(theme);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeSearchTerm, setActiveSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"received" | "given">("received");

  const handleSearch = () => {
    setActiveSearchTerm(searchTerm);
  };

  const handleSearchSubmit = () => {
    handleSearch();
  };

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      animationType="slide"
    >
      <View style={styles.modalContainer}>
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
          >
            <View style={styles.header}>
              <View style={{ width: 40 }} />
              <Text style={styles.headerTitle}>Likes</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <X size={24} color={theme.colors.foreground} />
              </TouchableOpacity>
            </View>

            <View style={styles.contentContainer}>
              {/* Toggle Section */}
              <View style={styles.toggleContainer}>
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    activeTab === "received"
                      ? styles.toggleButtonActive
                      : styles.toggleButtonInactive,
                  ]}
                  onPress={() => setActiveTab("received")}
                >
                  <Text
                    style={[
                      styles.toggleButtonText,
                      activeTab === "received"
                        ? styles.toggleButtonTextActive
                        : styles.toggleButtonTextInactive,
                    ]}
                  >
                    Received
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    activeTab === "given"
                      ? styles.toggleButtonActive
                      : styles.toggleButtonInactive,
                  ]}
                  onPress={() => setActiveTab("given")}
                >
                  <Text
                    style={[
                      styles.toggleButtonText,
                      activeTab === "given"
                        ? styles.toggleButtonTextActive
                        : styles.toggleButtonTextInactive,
                    ]}
                  >
                    Given
                  </Text>
                </TouchableOpacity>
              </View>
              {/* Search Container */}
              <View style={styles.searchContainer}>
                <View style={styles.searchInputContainer}>
                  <Search
                    size={20}
                    color={theme.colors.mutedForeground}
                    style={styles.searchIcon}
                  />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search likes..."
                    placeholderTextColor={theme.colors.mutedForeground}
                    value={searchTerm}
                    onChangeText={setSearchTerm}
                    onSubmitEditing={handleSearchSubmit}
                    returnKeyType="search"
                  />
                </View>
              </View>

              {/* List Container */}
              <View style={styles.listContainer}>
                {activeTab === "received" ? (
                  <BusinessLikesList
                    businessId={businessId}
                    searchTerm={activeSearchTerm}
                  />
                ) : (
                  <LikesList searchTerm={activeSearchTerm} />
                )}
              </View>
            </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
      </View>
    </Modal>
  );
}
