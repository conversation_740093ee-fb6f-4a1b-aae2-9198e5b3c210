import React from "react";
import { View } from "react-native";
import { SkeletonLoader } from "@/src/components/ui/SkeletonLoader";
import { useTheme } from "@/src/hooks/useTheme";

export const BusinessReviewsModalSkeleton = () => {
  const theme = useTheme();
  const { isDark } = theme;

  return (
    <View style={{ flex: 1 }}>
      {/* Toggle Skeleton */}
      <View
        style={{
          flexDirection: "row",
          marginHorizontal: theme.spacing.md,
          marginTop: theme.spacing.md,
          marginBottom: theme.spacing.sm,
          backgroundColor: theme.colors.card,
          borderRadius: theme.borderRadius.md,
          padding: theme.spacing.xs,
        }}
      >
        <View style={{ flex: 1, marginRight: theme.spacing.xs }}>
          <SkeletonLoader
            height={36}
            width="100%"
            borderRadius={theme.borderRadius.sm}
          />
        </View>
        <View style={{ flex: 1, marginLeft: theme.spacing.xs }}>
          <SkeletonLoader
            height={36}
            width="100%"
            borderRadius={theme.borderRadius.sm}
          />
        </View>
      </View>

      {/* Search and Sort Skeleton */}
      <View
        style={{
          paddingHorizontal: theme.spacing.md,
          paddingVertical: theme.spacing.sm,
        }}
      >
        {/* Search Skeleton */}
        <SkeletonLoader
          height={48}
          width="100%"
          borderRadius={theme.borderRadius.lg}
          style={{ marginBottom: theme.spacing.sm }}
        />
        {/* Sort Button Skeleton */}
        <View style={{ flexDirection: "row", justifyContent: "flex-end" }}>
          <SkeletonLoader
            height={36}
            width={120}
            borderRadius={theme.borderRadius.md}
          />
        </View>
      </View>

      {/* Review List Item Skeletons */}
      <View style={{ paddingHorizontal: theme.spacing.md, flex: 1 }}>
        {Array.from({ length: 6 }).map((_, index) => (
          <View key={index}>
            <View
              style={{
                backgroundColor: theme.colors.card,
                borderRadius: theme.borderRadius.lg,
                marginBottom: theme.spacing.md,
                borderWidth: 1,
                borderColor: theme.colors.border,
                overflow: "hidden",
              }}
            >
              {/* Review Header */}
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  paddingHorizontal: theme.spacing.md,
                  paddingVertical: theme.spacing.md,
                  borderBottomWidth: 1,
                  borderBottomColor: theme.colors.border,
                }}
              >
                {/* Avatar Skeleton */}
                <SkeletonLoader
                  height={44}
                  width={44}
                  borderRadius={22}
                  style={{ marginRight: theme.spacing.md }}
                />

                {/* Reviewer Info Skeleton */}
                <View style={{ flex: 1 }}>
                  <SkeletonLoader
                    height={18}
                    width="60%"
                    borderRadius={theme.borderRadius.sm}
                    style={{ marginBottom: theme.spacing.xs }}
                  />
                  <SkeletonLoader
                    height={14}
                    width="45%"
                    borderRadius={theme.borderRadius.sm}
                  />
                </View>

                {/* Rating Skeleton */}
                <SkeletonLoader
                  height={20}
                  width={100}
                  borderRadius={theme.borderRadius.sm}
                />
              </View>

              {/* Review Content */}
              <View
                style={{
                  paddingHorizontal: theme.spacing.md,
                  paddingVertical: theme.spacing.md,
                }}
              >
                {/* Review Text Skeleton */}
                <SkeletonLoader
                  height={16}
                  width="100%"
                  borderRadius={theme.borderRadius.sm}
                  style={{ marginBottom: theme.spacing.xs }}
                />
                <SkeletonLoader
                  height={16}
                  width="85%"
                  borderRadius={theme.borderRadius.sm}
                  style={{ marginBottom: theme.spacing.xs }}
                />
                <SkeletonLoader
                  height={16}
                  width="70%"
                  borderRadius={theme.borderRadius.sm}
                />
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default BusinessReviewsModalSkeleton;
