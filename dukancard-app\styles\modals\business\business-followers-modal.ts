import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createBusinessFollowersModalStyles = (theme: ReturnType<typeof useTheme>) => {
  const { colors, spacing, borderRadius, typography, isDark } = theme;

  return StyleSheet.create({
    // Modal container
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },

    // Header
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
    },
    closeButton: {
      padding: spacing.xs,
    },

    // Content container
    contentContainer: {
      flex: 1,
    },

    // Toggle container
    toggleContainer: {
      flexDirection: "row",
      marginHorizontal: spacing.md,
      marginTop: spacing.md,
      marginBottom: spacing.sm,
      backgroundColor: colors.card,
      borderRadius: borderRadius.md,
      padding: spacing.xs,
    },
    toggleButton: {
      flex: 1,
      paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      borderRadius: borderRadius.sm,
      alignItems: "center",
    },
    toggleButtonActive: {
      backgroundColor: colors.primary,
    },
    toggleButtonInactive: {
      backgroundColor: "transparent",
    },
    toggleButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: "600",
    },
    toggleButtonTextActive: {
      color: colors.primaryForeground,
    },
    toggleButtonTextInactive: {
      color: colors.mutedForeground,
    },

    // Search container
    searchContainer: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
    },
    searchInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.card,
      borderRadius: borderRadius.lg,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    searchInput: {
      flex: 1,
      fontSize: typography.fontSize.base,
      color: colors.foreground,
      marginLeft: spacing.sm,
    },
    searchIcon: {
      opacity: 0.6,
    },

    // List container
    listContainer: {
      flex: 1,
      paddingHorizontal: spacing.md,
    },

    // Follower card styles
    followerCard: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.sm,
      backgroundColor: colors.card,
      borderRadius: borderRadius.lg,
      marginBottom: spacing.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    followerCardAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: colors.muted,
      justifyContent: "center",
      alignItems: "center",
      marginRight: spacing.md,
    },
    followerCardAvatarImage: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    followerCardAvatarText: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.mutedForeground,
    },
    followerCardContent: {
      flex: 1,
    },
    followerCardName: {
      fontSize: typography.fontSize.base,
      fontWeight: "600",
      color: colors.foreground,
      marginBottom: spacing.xs,
    },
    followerCardType: {
      fontSize: typography.fontSize.sm,
      color: colors.mutedForeground,
    },
    followerCardLocation: {
      fontSize: typography.fontSize.sm,
      color: colors.mutedForeground,
      marginTop: spacing.xs,
    },
    followerCardActions: {
      flexDirection: "row",
      alignItems: "center",
      gap: spacing.sm,
    },
    actionButton: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
      borderWidth: 1,
    },
    followButton: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    unfollowButton: {
      backgroundColor: "transparent",
      borderColor: colors.border,
    },
    actionButtonText: {
      fontSize: typography.fontSize.sm,
      fontWeight: "600",
    },
    followButtonText: {
      color: colors.primaryForeground,
    },
    unfollowButtonText: {
      color: colors.foreground,
    },

    // Empty state
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: spacing.lg,
    },
    emptyIcon: {
      marginBottom: spacing.md,
    },
    emptyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
      textAlign: "center",
      marginBottom: spacing.sm,
    },
    emptyText: {
      fontSize: typography.fontSize.base,
      color: colors.mutedForeground,
      textAlign: "center",
      lineHeight: typography.lineHeight.relaxed,
    },

    // Loading states
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: spacing.xl,
    },
    footerLoadingContainer: {
      paddingVertical: spacing.lg,
      alignItems: "center",
    },

    // Error state
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: spacing.lg,
    },
    errorText: {
      fontSize: typography.fontSize.base,
      color: colors.destructive,
      textAlign: "center",
      marginBottom: spacing.md,
    },
    retryButton: {
      backgroundColor: colors.primary,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.sm,
      borderRadius: borderRadius.md,
    },
    retryButtonText: {
      color: colors.primaryForeground,
      fontSize: typography.fontSize.sm,
      fontWeight: "600",
    },
  });
};
