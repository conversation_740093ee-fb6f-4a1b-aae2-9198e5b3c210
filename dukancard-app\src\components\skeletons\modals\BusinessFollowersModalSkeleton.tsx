import React from "react";
import { View } from "react-native";
import { SkeletonLoader } from "@/src/components/ui/SkeletonLoader";
import { useTheme } from "@/src/hooks/useTheme";

export const BusinessFollowersModalSkeleton = () => {
  const theme = useTheme();
  const { isDark } = theme;

  return (
    <View style={{ flex: 1 }}>
      {/* Toggle Skeleton */}
      <View
        style={{
          flexDirection: "row",
          marginHorizontal: theme.spacing.md,
          marginTop: theme.spacing.md,
          marginBottom: theme.spacing.sm,
          backgroundColor: theme.colors.card,
          borderRadius: theme.borderRadius.md,
          padding: theme.spacing.xs,
        }}
      >
        <View style={{ flex: 1, marginRight: theme.spacing.xs }}>
          <SkeletonLoader
            height={36}
            width="100%"
            borderRadius={theme.borderRadius.sm}
          />
        </View>
        <View style={{ flex: 1, marginLeft: theme.spacing.xs }}>
          <SkeletonLoader
            height={36}
            width="100%"
            borderRadius={theme.borderRadius.sm}
          />
        </View>
      </View>

      {/* Search Skeleton */}
      <View
        style={{
          paddingHorizontal: theme.spacing.md,
          paddingVertical: theme.spacing.sm,
        }}
      >
        <SkeletonLoader
          height={48}
          width="100%"
          borderRadius={theme.borderRadius.lg}
        />
      </View>

      {/* Follower List Item Skeletons */}
      <View style={{ paddingHorizontal: theme.spacing.md, flex: 1 }}>
        {Array.from({ length: 8 }).map((_, index) => (
          <View key={index}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                paddingVertical: theme.spacing.md,
                paddingHorizontal: theme.spacing.sm,
                backgroundColor: theme.colors.card,
                borderRadius: theme.borderRadius.lg,
                marginBottom: theme.spacing.sm,
                borderWidth: 1,
                borderColor: theme.colors.border,
                minHeight: 82,
              }}
            >
              {/* Avatar Skeleton */}
              <SkeletonLoader
                height={50}
                width={50}
                borderRadius={25}
                style={{ marginRight: theme.spacing.md }}
              />

              {/* Content Skeleton */}
              <View style={{ flex: 1 }}>
                {/* Name Skeleton */}
                <SkeletonLoader
                  height={18}
                  width="65%"
                  borderRadius={theme.borderRadius.sm}
                  style={{ marginBottom: theme.spacing.xs }}
                />
                {/* Type Skeleton */}
                <SkeletonLoader
                  height={14}
                  width="40%"
                  borderRadius={theme.borderRadius.sm}
                  style={{ marginBottom: theme.spacing.xs }}
                />
                {/* Location Skeleton */}
                <SkeletonLoader
                  height={14}
                  width="55%"
                  borderRadius={theme.borderRadius.sm}
                />
              </View>

              {/* Action Buttons Skeleton */}
              <View style={{ flexDirection: "row", gap: theme.spacing.sm }}>
                <SkeletonLoader
                  height={32}
                  width={80}
                  borderRadius={theme.borderRadius.md}
                />
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default BusinessFollowersModalSkeleton;
