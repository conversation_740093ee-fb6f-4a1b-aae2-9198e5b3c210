{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/storage-paths.ts"], "sourcesContent": ["/**\r\n * Scalable Storage Path Utilities\r\n *\r\n * This module provides utilities for generating scalable storage paths\r\n * that can handle billions of users efficiently using hash-based distribution.\r\n */\r\n\r\n/**\r\n * Generate scalable user path using hash-based distribution\r\n *\r\n * @param userId - The user's UUID\r\n * @returns Scalable path: users/{prefix}/{midfix}/{userId}\r\n *\r\n * Example:\r\n * - Input: \"a1b2c3d4-e5f6-7890-abcd-ef1234567890\"\r\n * - Output: \"users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890\"\r\n */\r\nexport function getScalableUserPath(userId: string): string {\r\n  if (!userId || typeof userId !== 'string') {\r\n    throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);\r\n  }\r\n\r\n  if (userId.length < 4) {\r\n    throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);\r\n  }\r\n\r\n  const prefix = userId.substring(0, 2).toLowerCase();\r\n  const midfix = userId.substring(2, 4).toLowerCase();\r\n\r\n  return `users/${prefix}/${midfix}/${userId}`;\r\n}\r\n\r\n/**\r\n * Generate profile image path\r\n */\r\nexport function getProfileImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/profile/logo_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate product image path (legacy - for backward compatibility)\r\n * @deprecated Use getProductBaseImagePath or getProductVariantImagePath instead\r\n */\r\nexport function getProductImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate base product image path\r\n */\r\nexport function getProductBaseImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate product variant image path\r\n */\r\nexport function getProductVariantImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  variantId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate gallery image path\r\n */\r\nexport function getGalleryImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/gallery/gallery_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate post image path\r\n */\r\nexport function getPostImagePath(\r\n  userId: string,\r\n  postId: string,\r\n  imageIndex: number,\r\n  timestamp: number,\r\n  createdAt?: string\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n\r\n  // Use post creation date if provided, otherwise use current date (for backward compatibility)\r\n  const dateToUse = createdAt ? new Date(createdAt) : new Date();\r\n  const year = dateToUse.getFullYear();\r\n  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate post folder path for deletion\r\n */\r\nexport function getPostFolderPath(userId: string, postId: string, createdAt: string): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  const postDate = new Date(createdAt);\r\n  const year = postDate.getFullYear();\r\n  const month = String(postDate.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}`;\r\n}\r\n\r\n/**\r\n * Generate customer avatar image path\r\n */\r\nexport function getCustomerAvatarPath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/avatar/avatar_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate customer post image path\r\n */\r\nexport function getCustomerPostImagePath(\r\n  userId: string,\r\n  postId: string,\r\n  imageIndex: number,\r\n  timestamp: number,\r\n  createdAt?: string\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n\r\n  // Use post creation date if provided, otherwise use current date (for backward compatibility)\r\n  const dateToUse = createdAt ? new Date(createdAt) : new Date();\r\n  const year = dateToUse.getFullYear();\r\n  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate custom ad image path\r\n */\r\nexport function getCustomAdImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/ads/custom_ad_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate custom header image path\r\n */\r\nexport function getCustomHeaderImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/branding/header_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate theme-specific custom header image path\r\n */\r\nexport function getThemeSpecificHeaderImagePath(\r\n  userId: string,\r\n  timestamp: number,\r\n  theme: 'light' | 'dark'\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/branding/header_${theme}_${timestamp}.webp`;\r\n}\r\n\r\n// Legacy utilities removed since migration is complete\r\n\r\n/**\r\n * Path validation utilities\r\n */\r\nexport class PathValidator {\r\n  /**\r\n   * Validate if a path follows the new scalable structure\r\n   */\r\n  static isScalablePath(path: string): boolean {\r\n    return path.startsWith('users/') && path.split('/').length >= 4;\r\n  }\r\n\r\n  /**\r\n   * Extract user ID from scalable path\r\n   */\r\n  static extractUserIdFromPath(path: string): string | null {\r\n    if (!this.isScalablePath(path)) {\r\n      return null;\r\n    }\r\n\r\n    const parts = path.split('/');\r\n    return parts[3]; // users/{prefix}/{midfix}/{userId}/...\r\n  }\r\n\r\n  /**\r\n   * Validate path structure integrity\r\n   */\r\n  static validatePathStructure(userId: string, path: string): boolean {\r\n    const expectedUserPath = getScalableUserPath(userId);\r\n    return path.startsWith(expectedUserPath);\r\n  }\r\n}\r\n\r\n/**\r\n * Storage analytics utilities\r\n */\r\nexport class StorageAnalytics {\r\n  /**\r\n   * Get storage distribution info for monitoring\r\n   */\r\n  static getDistributionInfo(userId: string): {\r\n    prefix: string;\r\n    midfix: string;\r\n    bucket: string;\r\n    estimatedPeers: number;\r\n  } {\r\n    const prefix = userId.substring(0, 2).toLowerCase();\r\n    const midfix = userId.substring(2, 4).toLowerCase();\r\n\r\n    // Estimate number of users in same bucket (assuming even distribution)\r\n    const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets\r\n    const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users\r\n\r\n    return {\r\n      prefix,\r\n      midfix,\r\n      bucket: `${prefix}/${midfix}`,\r\n      estimatedPeers\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;;;;;;;;CASC;;;;;;;;;;;;;;;;;AACM,SAAS,oBAAoB,MAAc;IAChD,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,OAAO,OAAO,SAAS,EAAE,QAAQ;IAC3F;IAEA,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,MAAM,IAAI,MAAM,CAAC,yDAAyD,EAAE,QAAQ;IACtF;IAEA,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;IACjD,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;IAEjD,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ;AAC9C;AAKO,SAAS,oBAAoB,MAAc,EAAE,SAAiB;IACnE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,cAAc,EAAE,UAAU,KAAK,CAAC;AACrD;AAMO,SAAS,oBACd,MAAc,EACd,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAClF;AAKO,SAAS,wBACd,MAAc,EACd,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,YAAY,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AACvF;AAKO,SAAS,2BACd,MAAc,EACd,SAAiB,EACjB,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,CAAC,EAAE,UAAU,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC/F;AAKO,SAAS,oBAAoB,MAAc,EAAE,SAAiB;IACnE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,UAAU,KAAK,CAAC;AACxD;AAKO,SAAS,iBACd,MAAc,EACd,MAAc,EACd,UAAkB,EAClB,SAAiB,EACjB,SAAkB;IAElB,MAAM,WAAW,oBAAoB;IAErC,8FAA8F;IAC9F,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,IAAI;IACxD,MAAM,OAAO,UAAU,WAAW;IAClC,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE3D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC7F;AAKO,SAAS,kBAAkB,MAAc,EAAE,MAAc,EAAE,SAAiB;IACjF,MAAM,WAAW,oBAAoB;IACrC,MAAM,WAAW,IAAI,KAAK;IAC1B,MAAM,OAAO,SAAS,WAAW;IACjC,MAAM,QAAQ,OAAO,SAAS,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE1D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ;AACvD;AAKO,SAAS,sBAAsB,MAAc,EAAE,SAAiB;IACrE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,eAAe,EAAE,UAAU,KAAK,CAAC;AACtD;AAKO,SAAS,yBACd,MAAc,EACd,MAAc,EACd,UAAkB,EAClB,SAAiB,EACjB,SAAkB;IAElB,MAAM,WAAW,oBAAoB;IAErC,8FAA8F;IAC9F,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,IAAI;IACxD,MAAM,OAAO,UAAU,WAAW;IAClC,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE3D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC7F;AAKO,SAAS,qBAAqB,MAAc,EAAE,SAAiB;IACpE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,eAAe,EAAE,UAAU,KAAK,CAAC;AACtD;AAKO,SAAS,yBAAyB,MAAc,EAAE,SAAiB;IACxE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,UAAU,KAAK,CAAC;AACxD;AAKO,SAAS,gCACd,MAAc,EACd,SAAiB,EACjB,KAAuB;IAEvB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,MAAM,CAAC,EAAE,UAAU,KAAK,CAAC;AACjE;AAOO,MAAM;IACX;;GAEC,GACD,OAAO,eAAe,IAAY,EAAW;QAC3C,OAAO,KAAK,UAAU,CAAC,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,IAAI;IAChE;IAEA;;GAEC,GACD,OAAO,sBAAsB,IAAY,EAAiB;QACxD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO;YAC9B,OAAO;QACT;QAEA,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,OAAO,KAAK,CAAC,EAAE,EAAE,uCAAuC;IAC1D;IAEA;;GAEC,GACD,OAAO,sBAAsB,MAAc,EAAE,IAAY,EAAW;QAClE,MAAM,mBAAmB,oBAAoB;QAC7C,OAAO,KAAK,UAAU,CAAC;IACzB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,oBAAoB,MAAc,EAKvC;QACA,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;QACjD,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;QAEjD,uEAAuE;QACvE,MAAM,eAAe,KAAK,KAAK,KAAK,IAAI,iBAAiB;QACzD,MAAM,iBAAiB,KAAK,KAAK,CAAC,UAAU,eAAe,wBAAwB;QAEnF,OAAO;YACL;YACA;YACA,QAAQ,GAAG,OAAO,CAAC,EAAE,QAAQ;YAC7B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/upload-post-media.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { Database } from \"@/types/supabase\";\r\n\r\nimport { getPostImagePath, getPostFolderPath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface PostMediaUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload and process image for business post\r\n * Future-proof structure: {userId}/posts/{year}/{month}/{postId}/image_0_{timestamp}.webp\r\n */\r\nexport async function uploadPostImage(\r\n  formData: FormData,\r\n  postId: string,\r\n  postCreatedAt?: string\r\n): Promise<PostMediaUploadResult> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const userId = user.id;\r\n  const imageFile = formData.get(\"imageFile\") as File | null;\r\n\r\n  if (!imageFile) {\r\n    return { success: false, error: \"No image file provided.\" };\r\n  }\r\n\r\n  // Use the provided post creation date for consistent folder structure\r\n\r\n  // Validate file type (strict backend validation)\r\n  const allowedTypes = [\"image/jpeg\", \"image/png\", \"image/gif\", \"image/webp\"];\r\n  const allowedExtensions = [\".jpg\", \".jpeg\", \".png\", \".gif\", \".webp\"];\r\n\r\n  if (!allowedTypes.includes(imageFile.type)) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.\"\r\n    };\r\n  }\r\n\r\n  // Validate file extension as additional security measure\r\n  const fileName = imageFile.name.toLowerCase();\r\n  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));\r\n  if (!hasValidExtension) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid file extension. Please upload files with .jpg, .jpeg, .png, .gif, or .webp extensions.\"\r\n    };\r\n  }\r\n\r\n  // Backend size validation (critical security check)\r\n  const maxSize = 15 * 1024 * 1024; // 15MB - matches industry standards\r\n  if (imageFile.size > maxSize) {\r\n    const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(2);\r\n    return {\r\n      success: false,\r\n      error: `File size (${fileSizeMB}MB) exceeds the 15MB limit. Please choose a smaller image.`\r\n    };\r\n  }\r\n\r\n  // Additional security: Check for minimum file size (avoid empty files)\r\n  if (imageFile.size < 100) { // 100 bytes minimum\r\n    return {\r\n      success: false,\r\n      error: \"File appears to be empty or corrupted. Please try a different image.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Create scalable path structure for billions of users\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const bucketName = \"business\";\r\n    const imagePath = getPostImagePath(userId, postId, 0, timestamp, postCreatedAt);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createClient();\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const client = await adminSupabase;\r\n    const { error: uploadError } = await client.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Post Image Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL using admin client\r\n    const { data: urlData } = client.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Error processing post image:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Failed to process image. Please try a different image.\"\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Delete entire post folder and all its contents\r\n * This removes all files in the post folder, effectively deleting the folder\r\n */\r\nexport async function deletePostMedia(\r\n  userId: string,\r\n  postId: string,\r\n  createdAt: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // Use admin client for storage operations to bypass RLS\r\n  const adminSupabase = await createClient() as SupabaseClient<Database>;\r\n\r\n  try {\r\n    const bucketName = \"business\";\r\n    const postFolderPath = getPostFolderPath(userId, postId, createdAt);\r\n\r\n    // List all files in the post folder\r\n    const { data: files, error: listError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .list(postFolderPath, {\r\n        limit: 1000, // Set a reasonable limit for safety\r\n        sortBy: { column: 'name', order: 'asc' }\r\n      });\r\n\r\n    if (listError) {\r\n      // If folder doesn't exist, consider it successful (already deleted)\r\n      if (listError.message?.includes('not found') ||\r\n          listError.message?.includes('does not exist') ||\r\n          listError.message?.includes('The resource was not found')) {\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error listing post media files:\", listError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to list media files: ${listError.message}`,\r\n      };\r\n    }\r\n\r\n    if (!files || files.length === 0) {\r\n      // No files to delete, folder is already empty or doesn't exist\r\n      return { success: true };\r\n    }\r\n\r\n    // Create full paths for all files in the folder\r\n    const filePaths = files.map(file => `${postFolderPath}/${file.name}`);\r\n\r\n    // Delete all files in the post folder using admin client\r\n    // In object storage, deleting all files effectively removes the folder\r\n    const { error: deleteError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .remove(filePaths);\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting post folder contents:\", deleteError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to delete post folder: ${deleteError.message}`,\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Error in deletePostMedia:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred while deleting post folder.\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAIA;;;;;;AAYO,eAAe,gBACpB,QAAkB,EAClB,MAAc,EACd,aAAsB;IAEtB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,MAAM,SAAS,KAAK,EAAE;IACtB,MAAM,YAAY,SAAS,GAAG,CAAC;IAE/B,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,sEAAsE;IAEtE,iDAAiD;IACjD,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAC3E,MAAM,oBAAoB;QAAC;QAAQ;QAAS;QAAQ;QAAQ;KAAQ;IAEpE,IAAI,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,GAAG;QAC1C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yDAAyD;IACzD,MAAM,WAAW,UAAU,IAAI,CAAC,WAAW;IAC3C,MAAM,oBAAoB,kBAAkB,IAAI,CAAC,CAAA,MAAO,SAAS,QAAQ,CAAC;IAC1E,IAAI,CAAC,mBAAmB;QACtB,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,oDAAoD;IACpD,MAAM,UAAU,KAAK,OAAO,MAAM,oCAAoC;IACtE,IAAI,UAAU,IAAI,GAAG,SAAS;QAC5B,MAAM,aAAa,CAAC,UAAU,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;QAC5D,OAAO;YACL,SAAS;YACT,OAAO,CAAC,WAAW,EAAE,WAAW,0DAA0D,CAAC;QAC7F;IACF;IAEA,uEAAuE;IACvE,IAAI,UAAU,IAAI,GAAG,KAAK;QACxB,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI;QACF,uDAAuD;QACvD,MAAM,YAAY,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC1D,MAAM,aAAa;QACnB,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,QAAQ,GAAG,WAAW;QAEjE,4DAA4D;QAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,UAAU,WAAW;QAE1D,wDAAwD;QACxD,MAAM,gBAAgB,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAEjC,gDAAgD;QAChD,MAAM,SAAS,MAAM;QACrB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,OAAO,OAAO,CAChD,IAAI,CAAC,YACL,MAAM,CAAC,WAAW,YAAY;YAC7B,aAAa,UAAU,IAAI;YAC3B,QAAQ;QACV;QAEF,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,wBAAwB,EAAE,YAAY,OAAO,EAAE;YACzD;QACF;QAEA,wCAAwC;QACxC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,OAAO,OAAO,CACrC,IAAI,CAAC,YACL,YAAY,CAAC;QAEhB,IAAI,CAAC,SAAS,WAAW;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,KAAK,QAAQ,SAAS;QACxB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAMO,eAAe,gBACpB,MAAc,EACd,MAAc,EACd,SAAiB;IAEjB,wDAAwD;IACxD,MAAM,gBAAgB,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAEvC,IAAI;QACF,MAAM,aAAa;QACnB,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,QAAQ;QAEzD,oCAAoC;QACpC,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAAc,OAAO,CAClE,IAAI,CAAC,YACL,IAAI,CAAC,gBAAgB;YACpB,OAAO;YACP,QAAQ;gBAAE,QAAQ;gBAAQ,OAAO;YAAM;QACzC;QAEF,IAAI,WAAW;YACb,oEAAoE;YACpE,IAAI,UAAU,OAAO,EAAE,SAAS,gBAC5B,UAAU,OAAO,EAAE,SAAS,qBAC5B,UAAU,OAAO,EAAE,SAAS,+BAA+B;gBAC7D,OAAO;oBAAE,SAAS;gBAAK;YACzB;YACA,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,4BAA4B,EAAE,UAAU,OAAO,EAAE;YAC3D;QACF;QAEA,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAChC,+DAA+D;YAC/D,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,gDAAgD;QAChD,MAAM,YAAY,MAAM,GAAG,CAAC,CAAA,OAAQ,GAAG,eAAe,CAAC,EAAE,KAAK,IAAI,EAAE;QAEpE,yDAAyD;QACzD,uEAAuE;QACvE,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,YACL,MAAM,CAAC;QAEV,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,8BAA8B,EAAE,YAAY,OAAO,EAAE;YAC/D;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IA7LsB;IA4HA;;AA5HA,+OAAA;AA4HA,+OAAA", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/crud.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { PostFormData } from '@/lib/types/posts';\r\nimport { ActionResponse } from '@/lib/types/api';\r\nimport { deletePostMedia } from '@/lib/actions/shared/upload-post-media';\r\n\r\n/**\r\n * Create a new post\r\n */\r\nexport async function createPost(formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to create a post'\r\n    };\r\n  }\r\n\r\n  // Get the user's business profile\r\n  const { data: businessProfile, error: profileError } = await supabase\r\n    .from('business_profiles')\r\n    .select('id, city_slug, state_slug, locality_slug, pincode, logo_url')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError || !businessProfile) {\r\n    return {\r\n      success: false,\r\n      message: 'Business profile not found',\r\n      error: 'You must have a business profile to create a post'\r\n    };\r\n  }\r\n\r\n  // Prepare post data\r\n  const postData = {\r\n    business_id: user.id,\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    city_slug: businessProfile.city_slug,\r\n    state_slug: businessProfile.state_slug,\r\n    locality_slug: businessProfile.locality_slug,\r\n    pincode: businessProfile.pincode,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    author_avatar: businessProfile.logo_url\r\n  };\r\n\r\n  // Insert the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .insert(postData)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error creating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to create post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post created successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the content of an existing post (for inline editing)\r\n */\r\nexport async function updatePostContent(postId: string, content: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the content and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      content: content.trim(),\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post content:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the product_ids of an existing post (for inline editing)\r\n */\r\nexport async function updatePostProducts(postId: string, productIds: string[]): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the product_ids and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      product_ids: productIds,\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post products:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post products',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post products updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update an existing post (full update for form submissions)\r\n */\r\nexport async function updatePost(postId: string, formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Prepare update data\r\n  const updateData = {\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    updated_at: new Date().toISOString()\r\n  };\r\n\r\n  // Update the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update(updateData)\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Delete a post\r\n */\r\nexport async function deletePost(postId: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to delete a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user, get creation date for media deletion\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id, created_at, image_url')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to delete it'\r\n    };\r\n  }\r\n\r\n  // First, attempt to delete the post folder from storage\r\n  // This ensures we clean up any files that might exist, regardless of image_url status\r\n  try {\r\n    const mediaDeleteResult = await deletePostMedia(user.id, postId, existingPost.created_at);\r\n    if (!mediaDeleteResult.success && mediaDeleteResult.error) {\r\n      console.error('Error deleting business post media:', mediaDeleteResult.error);\r\n      return {\r\n        success: false,\r\n        message: 'Failed to delete post images',\r\n        error: `Cannot delete post: ${mediaDeleteResult.error}`\r\n      };\r\n    }\r\n  } catch (mediaError) {\r\n    console.error('Error deleting business post media:', mediaError);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to delete post images',\r\n      error: 'Cannot delete post: Failed to clean up associated images'\r\n    };\r\n  }\r\n\r\n  // Only delete the post after successful media deletion\r\n  const { error } = await supabase\r\n    .from('business_posts')\r\n    .delete()\r\n    .eq('id', postId);\r\n\r\n  if (error) {\r\n    console.error('Error deleting post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to delete post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post deleted successfully'\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAGA;;;;;;;AAKO,eAAe,WAAW,QAAsB;IACrD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,qBACL,MAAM,CAAC,+DACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,gBAAgB,CAAC,iBAAiB;QACpC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,WAAW;QACf,aAAa,KAAK,EAAE;QACpB,SAAS,SAAS,OAAO;QACzB,WAAW,SAAS,SAAS,IAAI;QACjC,WAAW,gBAAgB,SAAS;QACpC,YAAY,gBAAgB,UAAU;QACtC,eAAe,gBAAgB,aAAa;QAC5C,SAAS,gBAAgB,OAAO;QAChC,aAAa,SAAS,WAAW,IAAI,EAAE;QACvC,wBAAwB,SAAS,sBAAsB,IAAI,EAAE;QAC7D,eAAe,gBAAgB,QAAQ;IACzC;IAEA,kBAAkB;IAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,kBAAkB,MAAc,EAAE,OAAe;IACrE,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,wCAAwC;IACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;QACN,SAAS,QAAQ,IAAI;QACrB,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,mBAAmB,MAAc,EAAE,UAAoB;IAC3E,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,4CAA4C;IAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;QACN,aAAa;QACb,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,WAAW,MAAc,EAAE,QAAsB;IACrE,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,sBAAsB;IACtB,MAAM,aAAa;QACjB,SAAS,SAAS,OAAO;QACzB,WAAW,SAAS,SAAS,IAAI;QACjC,aAAa,SAAS,WAAW,IAAI,EAAE;QACvC,wBAAwB,SAAS,sBAAsB,IAAI,EAAE;QAC7D,YAAY,IAAI,OAAO,WAAW;IACpC;IAEA,kBAAkB;IAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,WAAW,MAAc;IAC7C,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,yFAAyF;IACzF,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,6BACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,wDAAwD;IACxD,sFAAsF;IACtF,IAAI;QACF,MAAM,oBAAoB,MAAM,CAAA,GAAA,mJAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE,EAAE,QAAQ,aAAa,UAAU;QACxF,IAAI,CAAC,kBAAkB,OAAO,IAAI,kBAAkB,KAAK,EAAE;YACzD,QAAQ,KAAK,CAAC,uCAAuC,kBAAkB,KAAK;YAC5E,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,CAAC,oBAAoB,EAAE,kBAAkB,KAAK,EAAE;YACzD;QACF;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,uDAAuD;IACvD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF;;;IA7VsB;IAyEA;IAiEA;IAiEA;IAuEA;;AAlRA,+OAAA;AAyEA,+OAAA;AAiEA,+OAAA;AAiEA,+OAAA;AAuEA,+OAAA", "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/productActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\nimport { Tables } from \"@/types/supabase\";\r\n\r\ntype ProductsServicesRow = Tables<'products_services'>;\r\n\r\n// Search business products for the current user\r\nexport async function searchBusinessProducts(query: string): Promise<{\r\n  success: boolean;\r\n  data?: ProductsServicesRow[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!query || query.trim().length < 2) {\r\n      return {\r\n        success: false,\r\n        error: \"Search query must be at least 2 characters long\",\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const {\r\n      data: { user },\r\n      error: authError,\r\n    } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Use admin client to bypass RLS policies\r\n    const adminSupabase = await createClient();\r\n\r\n    // Search products for the current user's business only\r\n    const { data, error } = await adminSupabase\r\n      .from(\"products_services\")\r\n      .select(\"*\")\r\n      .eq(\"business_id\", user.id) // Ensure user can only see their own products\r\n      .eq(\"is_available\", true)\r\n      .ilike(\"name\", `%${query.trim()}%`)\r\n      .order(\"name\", { ascending: true })\r\n      .limit(10); // Limit search results to 10 items\r\n\r\n    if (error) {\r\n      console.error(\"Error searching products:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to search products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in searchBusinessProducts:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get selected products by IDs for the current user\r\nexport async function getSelectedProducts(productIds: string[]): Promise<{\r\n  success: boolean;\r\n  data?: ProductsServicesRow[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!productIds || productIds.length === 0) {\r\n      return {\r\n        success: true,\r\n        data: [],\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const {\r\n      data: { user },\r\n      error: authError,\r\n    } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Use regular client - accessing user's own products with authentication\r\n    // Get products by IDs, but only for the current user's business\r\n    const { data, error } = await supabase\r\n      .from(\"products_services\")\r\n      .select(\"*\")\r\n      .in(\"id\", productIds)\r\n      .eq(\"business_id\", user.id) // Ensure user can only access their own products\r\n      .order(\"name\", { ascending: true });\r\n\r\n    if (error) {\r\n      console.error(\"Error getting selected products:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to get selected products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in getSelectedProducts:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;;;;;AAOO,eAAe,uBAAuB,KAAa;IAKxD,IAAI;QACF,iBAAiB;QACjB,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,yBAAyB;QACzB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,0CAA0C;QAC1C,MAAM,gBAAgB,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAEvC,uDAAuD;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,KAAK,EAAE,EAAE,8CAA8C;SACzE,EAAE,CAAC,gBAAgB,MACnB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC,EACjC,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK,GAChC,KAAK,CAAC,KAAK,mCAAmC;QAEjD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,QAAQ,EAAE;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAGO,eAAe,oBAAoB,UAAoB;IAK5D,IAAI;QACF,iBAAiB;QACjB,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;YAC1C,OAAO;gBACL,SAAS;gBACT,MAAM,EAAE;YACV;QACF;QAEA,yBAAyB;QACzB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,yEAAyE;QACzE,gEAAgE;QAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,eAAe,KAAK,EAAE,EAAE,iDAAiD;SAC5E,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,QAAQ,EAAE;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IAvHsB;IA+DA;;AA/DA,+OAAA;AA+DA,+OAAA", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/products/fetchProductsByIds.ts"], "sourcesContent": ["\"use server\";\n\nimport { createClient } from \"@/utils/supabase/server\";\nimport { Tables } from \"@/types/supabase\";\n\ntype ProductsServices = Tables<'products_services'>;\n\ntype FetchedProduct = Pick<\n  ProductsServices,\n  \"id\" | \"name\" | \"base_price\" | \"discounted_price\" | \"image_url\" | \"slug\"\n>;\n\n/**\n * Fetch products by their IDs using admin client to bypass RLS\n * This is used for displaying linked products in feed posts\n */\nexport async function fetchProductsByIds(productIds: string[]): Promise<{\n  success: boolean;\n  data?: FetchedProduct[];\n  error?: string;\n}> {\n  if (!productIds || productIds.length === 0) {\n    return {\n      success: true,\n      data: [],\n    };\n  }\n\n  try {\n    // Use admin client to bypass RLS policies\n    const supabase = await createClient();\n\n    const { data, error } = await supabase\n      .from(\"products_services\")\n      .select(\"id, name, base_price, discounted_price, image_url, slug\")\n      .in(\"id\", productIds)\n      .eq(\"is_available\", true);\n\n    if (error) {\n      console.error(\"Error fetching products by IDs:\", error);\n      return {\n        success: false,\n        error: \"Failed to fetch products\",\n      };\n    }\n\n    return {\n      success: true,\n      data: data || [],\n    };\n  } catch (error) {\n    console.error(\"Error in fetchProductsByIds:\", error);\n    return {\n      success: false,\n      error: \"An unexpected error occurred\",\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;;;AAcO,eAAe,mBAAmB,UAAoB;IAK3D,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,OAAO;YACL,SAAS;YACT,MAAM,EAAE;QACV;IACF;IAEA,IAAI;QACF,0CAA0C;QAC1C,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,2DACP,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,gBAAgB;QAEtB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,QAAQ,EAAE;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IAzCsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/business/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '00d22d305443621794e42caebec5f5b2c3348e1da0'} from 'ACTIONS_MODULE0'\nexport {createPost as '40f139c754ec4c6ff7582d5b7da38f3b0b7f89183a'} from 'ACTIONS_MODULE1'\nexport {updatePost as '6052d8a20c3dcf4587afef28181133eb619ca98631'} from 'ACTIONS_MODULE1'\nexport {searchBusinessProducts as '407beb66cbd0f844cfc9cb9fe59bda949b4893e6f0'} from 'ACTIONS_MODULE2'\nexport {getSelectedProducts as '40e3d5246f60986e4cec74b928a2e1c984ac881349'} from 'ACTIONS_MODULE2'\nexport {fetchProductsByIds as '40ea4b864399c6c9d9078b90374133816d37f639d3'} from 'ACTIONS_MODULE3'\nexport {deletePost as '4050e80e371fefc877473f77b272054bf04a513935'} from 'ACTIONS_MODULE1'\nexport {updatePostContent as '601a261b57a6f2f76ac2635390b442b753a95411a5'} from 'ACTIONS_MODULE1'\nexport {updatePostProducts as '608eff919ada39933390f7b81249b16186cd931a71'} from 'ACTIONS_MODULE1'\n"], "names": [], "mappings": ";AAAA;AACA;AAEA;AAEA", "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/ModernBusinessFeedList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/feed/ModernBusinessFeedList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/feed/ModernBusinessFeedList.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/ModernBusinessFeedList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/feed/ModernBusinessFeedList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/feed/ModernBusinessFeedList.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr';\nimport { Database } from '@/types/supabase';\n\nexport function createClient() {\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n  if (!supabaseUrl || !supabaseAnonKey) {\n    console.error(\"Supabase environment variables are not set. Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are defined.\");\n    return createBrowserClient<Database>(\"\", \"\");\n  }\n\n  return createBrowserClient<Database>(\n    supabaseUrl,\n    supabaseAnonKey\n  );\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,SAAS;IACd,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAGtC;IAEA,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EACvB,aACA;AAEJ", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/constants.ts"], "sourcesContent": ["// lib/supabase/constants.ts\r\n\r\nexport const TABLES = {\r\n  BLOGS: \"blogs\",\r\n  BUSINESS_ACTIVITIES: \"business_activities\",\r\n  BUSINESS_PROFILES: \"business_profiles\",\r\n  CARD_VISITS: \"card_visits\",\r\n  CUSTOMER_POSTS: \"customer_posts\",\r\n  CUSTOMER_PROFILES: \"customer_profiles\",\r\n  LIKES: \"likes\",\r\n  PAYMENT_SUBSCRIPTIONS: \"payment_subscriptions\",\r\n  PINCODES: \"pincodes\",\r\n  PRODUCTS_SERVICES: \"products_services\",\r\n  PRODUCT_VARIANTS: \"product_variants\",\r\n  STORAGE_CLEANUP_CONFIG: \"storage_cleanup_config\",\r\n  STORAGE_CLEANUP_PROGRESS: \"storage_cleanup_progress\",\r\n  SUBSCRIPTIONS: \"subscriptions\",\r\n  SYSTEM_ALERTS: \"system_alerts\",\r\n  RATINGS_REVIEWS: \"ratings_reviews\",\r\n} as const;\r\n\r\nexport const BUCKETS = {\r\n  BUSINESS: \"business\",\r\n  CUSTOMERS: \"customers\",\r\n} as const;\r\n\r\nexport const COLUMNS = {\r\n  ID: \"id\",\r\n  CREATED_AT: \"created_at\",\r\n  UPDATED_AT: \"updated_at\",\r\n  NAME: \"name\",\r\n  EMAIL: \"email\",\r\n  PHONE: \"phone\",\r\n  CITY: \"city\",\r\n  STATE: \"state\",\r\n  PINCODE: \"pincode\",\r\n  PLAN_ID: \"plan_id\",\r\n  LOCALITY: \"locality\",\r\n  CITY_SLUG: \"city_slug\",\r\n  STATE_SLUG: \"state_slug\",\r\n  LOCALITY_SLUG: \"locality_slug\",\r\n  LOGO_URL: \"logo_url\",\r\n  IMAGE_URL: \"image_url\",\r\n  IMAGES: \"images\",\r\n  SLUG: \"slug\",\r\n  STATUS: \"status\",\r\n  CONTENT: \"content\",\r\n  GALLERY: \"gallery\",\r\n  DESCRIPTION: \"description\",\r\n  TITLE: \"title\",\r\n  USER_ID: \"user_id\",\r\n  BUSINESS_ID: \"business_id\",\r\n  BUSINESS_NAME: \"business_name\",\r\n  BUSINESS_SLUG: \"business_slug\",\r\n  PRODUCT_ID: \"product_id\",\r\n  PRODUCT_TYPE: \"product_type\",\r\n  BUSINESS_PROFILE_ID: \"business_profile_id\",\r\n  RAZORPAY_SUBSCRIPTION_ID: \"razorpay_subscription_id\",\r\n  SUBSCRIPTION_STATUS: \"subscription_status\",\r\n  RATING: \"rating\",\r\n  REVIEW_TEXT: \"review_text\",\r\n  AVATAR_URL: \"avatar_url\",\r\n  ADDRESS_LINE: \"address_line\"\r\n} as const;"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;AAErB,MAAM,SAAS;IACpB,OAAO;IACP,qBAAqB;IACrB,mBAAmB;IACnB,aAAa;IACb,gBAAgB;IAChB,mBAAmB;IACnB,OAAO;IACP,uBAAuB;IACvB,UAAU;IACV,mBAAmB;IACnB,kBAAkB;IAClB,wBAAwB;IACxB,0BAA0B;IAC1B,eAAe;IACf,eAAe;IACf,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,UAAU;IACV,WAAW;AACb;AAEO,MAAM,UAAU;IACrB,IAAI;IACJ,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,SAAS;IACT,UAAU;IACV,WAAW;IACX,YAAY;IACZ,eAAe;IACf,UAAU;IACV,WAAW;IACX,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,SAAS;IACT,aAAa;IACb,eAAe;IACf,eAAe;IACf,YAAY;IACZ,cAAc;IACd,qBAAqB;IACrB,0BAA0B;IAC1B,qBAAqB;IACrB,QAAQ;IACR,aAAa;IACb,YAAY;IACZ,cAAc;AAChB", "debugId": null}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/planPrioritizer.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Plan Prioritizer - Handles business plan-based post prioritization\r\n * Higher tier businesses get better visibility while maintaining fairness\r\n */\r\n\r\nexport const PLAN_PRIORITY: Record<string, number> = {\r\n  'enterprise': 5,\r\n  'pro': 4,\r\n  'growth': 3,\r\n  'basic': 2,\r\n  'free': 1\r\n};\r\n\r\nexport interface BusinessGroup {\r\n  authorId: string;\r\n  priority: number;\r\n  latestPostTime: number;\r\n  posts: UnifiedPost[];\r\n}\r\n\r\n/**\r\n * Create business priority groups based on subscription plans\r\n * Similar to how LinkedIn prioritizes premium content\r\n */\r\nexport function createBusinessPriorityGroups(businessPosts: UnifiedPost[]): BusinessGroup[] {\r\n  // Group posts by business author\r\n  const businessPostsByAuthor = new Map<string, UnifiedPost[]>();\r\n  businessPosts.forEach(post => {\r\n    if (!businessPostsByAuthor.has(post.author_id)) {\r\n      businessPostsByAuthor.set(post.author_id, []);\r\n    }\r\n    businessPostsByAuthor.get(post.author_id)!.push(post);\r\n  });\r\n\r\n  // Sort posts within each business group chronologically (latest first)\r\n  businessPostsByAuthor.forEach((posts, authorId) => {\r\n    businessPostsByAuthor.set(authorId, posts.sort((a, b) =>\r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n\r\n  // Create priority groups\r\n  return Array.from(businessPostsByAuthor.entries())\r\n    .map(([authorId, authorPosts]) => {\r\n      const latestPost = authorPosts[0];\r\n      const priority = PLAN_PRIORITY[latestPost.business_plan || 'free'] || 1;\r\n      return {\r\n        authorId,\r\n        priority,\r\n        latestPostTime: new Date(latestPost.created_at).getTime(),\r\n        posts: authorPosts\r\n      };\r\n    })\r\n    .sort((a, b) => {\r\n      // Sort by plan priority first\r\n      if (a.priority !== b.priority) {\r\n        return b.priority - a.priority; // Higher priority first\r\n      }\r\n      // If same plan, sort by latest post timestamp\r\n      return b.latestPostTime - a.latestPostTime;\r\n    });\r\n}\r\n\r\n/**\r\n * Distribute business posts with plan-based prioritization\r\n * Uses tier-based round-robin to ensure diversity within each plan level\r\n */\r\nexport function distributePrioritizedBusinessPosts(businessGroups: BusinessGroup[]): UnifiedPost[] {\r\n  const result: UnifiedPost[] = [];\r\n\r\n  // Group businesses by plan priority\r\n  const businessesByPlan = new Map<number, BusinessGroup[]>();\r\n  businessGroups.forEach(business => {\r\n    if (!businessesByPlan.has(business.priority)) {\r\n      businessesByPlan.set(business.priority, []);\r\n    }\r\n    businessesByPlan.get(business.priority)!.push(business);\r\n  });\r\n\r\n  // Sort plan priorities (highest first)\r\n  const sortedPlanPriorities = Array.from(businessesByPlan.keys()).sort((a, b) => b - a);\r\n\r\n  // Distribute posts: round-robin within each plan tier\r\n  for (const planPriority of sortedPlanPriorities) {\r\n    const businessesInPlan = businessesByPlan.get(planPriority)!;\r\n\r\n    // Create queues for round-robin distribution\r\n    const businessPostQueues = businessesInPlan.map(business => ({\r\n      ...business,\r\n      remainingPosts: [...business.posts]\r\n    }));\r\n\r\n    // Round-robin within this plan tier until all posts are distributed\r\n    while (businessPostQueues.some(queue => queue.remainingPosts.length > 0)) {\r\n      businessPostQueues.forEach(business => {\r\n        if (business.remainingPosts.length > 0) {\r\n          const post = business.remainingPosts.shift()!;\r\n          result.push(post);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Get plan display name for UI purposes\r\n */\r\nexport function getPlanDisplayName(planId: string): string {\r\n  const planNames: Record<string, string> = {\r\n    'enterprise': 'Enterprise',\r\n    'pro': 'Pro',\r\n    'growth': 'Growth',\r\n    'basic': 'Basic',\r\n    'free': 'Free'\r\n  };\r\n  \r\n  return planNames[planId] || 'Free';\r\n}\r\n\r\n/**\r\n * Check if a business has premium features based on plan\r\n */\r\nexport function hasPremiumFeatures(planId: string): boolean {\r\n  const priority = PLAN_PRIORITY[planId] || 1;\r\n  return priority >= PLAN_PRIORITY.growth; // Growth and above\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAOO,MAAM,gBAAwC;IACnD,cAAc;IACd,OAAO;IACP,UAAU;IACV,SAAS;IACT,QAAQ;AACV;AAaO,SAAS,6BAA6B,aAA4B;IACvE,iCAAiC;IACjC,MAAM,wBAAwB,IAAI;IAClC,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,CAAC,sBAAsB,GAAG,CAAC,KAAK,SAAS,GAAG;YAC9C,sBAAsB,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QAC9C;QACA,sBAAsB,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IAClD;IAEA,uEAAuE;IACvE,sBAAsB,OAAO,CAAC,CAAC,OAAO;QACpC,sBAAsB,GAAG,CAAC,UAAU,MAAM,IAAI,CAAC,CAAC,GAAG,IACjD,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,yBAAyB;IACzB,OAAO,MAAM,IAAI,CAAC,sBAAsB,OAAO,IAC5C,GAAG,CAAC,CAAC,CAAC,UAAU,YAAY;QAC3B,MAAM,aAAa,WAAW,CAAC,EAAE;QACjC,MAAM,WAAW,aAAa,CAAC,WAAW,aAAa,IAAI,OAAO,IAAI;QACtE,OAAO;YACL;YACA;YACA,gBAAgB,IAAI,KAAK,WAAW,UAAU,EAAE,OAAO;YACvD,OAAO;QACT;IACF,GACC,IAAI,CAAC,CAAC,GAAG;QACR,8BAA8B;QAC9B,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;YAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ,EAAE,wBAAwB;QAC1D;QACA,8CAA8C;QAC9C,OAAO,EAAE,cAAc,GAAG,EAAE,cAAc;IAC5C;AACJ;AAMO,SAAS,mCAAmC,cAA+B;IAChF,MAAM,SAAwB,EAAE;IAEhC,oCAAoC;IACpC,MAAM,mBAAmB,IAAI;IAC7B,eAAe,OAAO,CAAC,CAAA;QACrB,IAAI,CAAC,iBAAiB,GAAG,CAAC,SAAS,QAAQ,GAAG;YAC5C,iBAAiB,GAAG,CAAC,SAAS,QAAQ,EAAE,EAAE;QAC5C;QACA,iBAAiB,GAAG,CAAC,SAAS,QAAQ,EAAG,IAAI,CAAC;IAChD;IAEA,uCAAuC;IACvC,MAAM,uBAAuB,MAAM,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAEpF,sDAAsD;IACtD,KAAK,MAAM,gBAAgB,qBAAsB;QAC/C,MAAM,mBAAmB,iBAAiB,GAAG,CAAC;QAE9C,6CAA6C;QAC7C,MAAM,qBAAqB,iBAAiB,GAAG,CAAC,CAAA,WAAY,CAAC;gBAC3D,GAAG,QAAQ;gBACX,gBAAgB;uBAAI,SAAS,KAAK;iBAAC;YACrC,CAAC;QAED,oEAAoE;QACpE,MAAO,mBAAmB,IAAI,CAAC,CAAA,QAAS,MAAM,cAAc,CAAC,MAAM,GAAG,GAAI;YACxE,mBAAmB,OAAO,CAAC,CAAA;gBACzB,IAAI,SAAS,cAAc,CAAC,MAAM,GAAG,GAAG;oBACtC,MAAM,OAAO,SAAS,cAAc,CAAC,KAAK;oBAC1C,OAAO,IAAI,CAAC;gBACd;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAoC;QACxC,cAAc;QACd,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,WAAW,aAAa,CAAC,OAAO,IAAI;IAC1C,OAAO,YAAY,cAAc,MAAM,EAAE,mBAAmB;AAC9D", "debugId": null}}, {"offset": {"line": 1166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/diversityEngine.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Diversity Engine - Ensures no consecutive posts from the same author\r\n * Inspired by Facebook/Instagram feed algorithms that maintain user engagement\r\n * through content diversity and prevent feed monotony.\r\n */\r\n\r\nexport interface DiversityOptions {\r\n  maxConsecutiveFromSameAuthor?: number;\r\n  prioritizeRecency?: boolean;\r\n}\r\n\r\n/**\r\n * Apply diversity rules to prevent consecutive posts from the same author\r\n * Uses a sliding window approach similar to major social media platforms\r\n */\r\nexport function applyDiversityRules(\r\n  posts: UnifiedPost[], \r\n  options: DiversityOptions = {}\r\n): UnifiedPost[] {\r\n  const { maxConsecutiveFromSameAuthor = 1 } = options;\r\n  \r\n  if (posts.length <= 1) return posts;\r\n\r\n  const diversifiedPosts: UnifiedPost[] = [];\r\n  const remainingPosts = [...posts];\r\n  let lastAuthorId: string | null = null;\r\n  let consecutiveCount = 0;\r\n\r\n  while (remainingPosts.length > 0) {\r\n    let selectedIndex = -1;\r\n    \r\n    // First, try to find a post from a different author\r\n    for (let i = 0; i < remainingPosts.length; i++) {\r\n      const post = remainingPosts[i];\r\n      \r\n      if (post.author_id !== lastAuthorId) {\r\n        selectedIndex = i;\r\n        break;\r\n      }\r\n    }\r\n    \r\n    // If no different author found, or we haven't exceeded consecutive limit\r\n    if (selectedIndex === -1 && consecutiveCount < maxConsecutiveFromSameAuthor) {\r\n      selectedIndex = 0; // Take the first available post\r\n    }\r\n    \r\n    // If still no selection, force diversity by taking first different author\r\n    if (selectedIndex === -1) {\r\n      for (let i = 0; i < remainingPosts.length; i++) {\r\n        if (remainingPosts[i].author_id !== lastAuthorId) {\r\n          selectedIndex = i;\r\n          break;\r\n        }\r\n      }\r\n      // If still no different author, take first available (edge case)\r\n      if (selectedIndex === -1) selectedIndex = 0;\r\n    }\r\n\r\n    const selectedPost = remainingPosts.splice(selectedIndex, 1)[0];\r\n    diversifiedPosts.push(selectedPost);\r\n    \r\n    // Update tracking variables\r\n    if (selectedPost.author_id === lastAuthorId) {\r\n      consecutiveCount++;\r\n    } else {\r\n      consecutiveCount = 1;\r\n      lastAuthorId = selectedPost.author_id;\r\n    }\r\n  }\r\n\r\n  return diversifiedPosts;\r\n}\r\n\r\n/**\r\n * Group posts by author while maintaining chronological order within groups\r\n */\r\nexport function groupPostsByAuthor(posts: UnifiedPost[]): Map<string, UnifiedPost[]> {\r\n  const grouped = new Map<string, UnifiedPost[]>();\r\n  \r\n  posts.forEach(post => {\r\n    if (!grouped.has(post.author_id)) {\r\n      grouped.set(post.author_id, []);\r\n    }\r\n    grouped.get(post.author_id)!.push(post);\r\n  });\r\n  \r\n  // Sort posts within each group chronologically (latest first)\r\n  grouped.forEach((authorPosts, authorId) => {\r\n    grouped.set(authorId, authorPosts.sort((a, b) => \r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n  \r\n  return grouped;\r\n}\r\n\r\n/**\r\n * Round-robin distribution to ensure fair representation\r\n * Similar to how Instagram distributes stories from different accounts\r\n */\r\nexport function roundRobinDistribution(groupedPosts: Map<string, UnifiedPost[]>): UnifiedPost[] {\r\n  const result: UnifiedPost[] = [];\r\n  const queues = Array.from(groupedPosts.entries()).map(([authorId, posts]) => ({\r\n    authorId,\r\n    posts: [...posts]\r\n  }));\r\n\r\n  // Continue until all queues are empty\r\n  while (queues.some(queue => queue.posts.length > 0)) {\r\n    queues.forEach(queue => {\r\n      if (queue.posts.length > 0) {\r\n        const post = queue.posts.shift()!;\r\n        result.push(post);\r\n      }\r\n    });\r\n  }\r\n\r\n  return result;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAiBO,SAAS,oBACd,KAAoB,EACpB,UAA4B,CAAC,CAAC;IAE9B,MAAM,EAAE,+BAA+B,CAAC,EAAE,GAAG;IAE7C,IAAI,MAAM,MAAM,IAAI,GAAG,OAAO;IAE9B,MAAM,mBAAkC,EAAE;IAC1C,MAAM,iBAAiB;WAAI;KAAM;IACjC,IAAI,eAA8B;IAClC,IAAI,mBAAmB;IAEvB,MAAO,eAAe,MAAM,GAAG,EAAG;QAChC,IAAI,gBAAgB,CAAC;QAErB,oDAAoD;QACpD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,MAAM,OAAO,cAAc,CAAC,EAAE;YAE9B,IAAI,KAAK,SAAS,KAAK,cAAc;gBACnC,gBAAgB;gBAChB;YACF;QACF;QAEA,yEAAyE;QACzE,IAAI,kBAAkB,CAAC,KAAK,mBAAmB,8BAA8B;YAC3E,gBAAgB,GAAG,gCAAgC;QACrD;QAEA,0EAA0E;QAC1E,IAAI,kBAAkB,CAAC,GAAG;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;gBAC9C,IAAI,cAAc,CAAC,EAAE,CAAC,SAAS,KAAK,cAAc;oBAChD,gBAAgB;oBAChB;gBACF;YACF;YACA,iEAAiE;YACjE,IAAI,kBAAkB,CAAC,GAAG,gBAAgB;QAC5C;QAEA,MAAM,eAAe,eAAe,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE;QAC/D,iBAAiB,IAAI,CAAC;QAEtB,4BAA4B;QAC5B,IAAI,aAAa,SAAS,KAAK,cAAc;YAC3C;QACF,OAAO;YACL,mBAAmB;YACnB,eAAe,aAAa,SAAS;QACvC;IACF;IAEA,OAAO;AACT;AAKO,SAAS,mBAAmB,KAAoB;IACrD,MAAM,UAAU,IAAI;IAEpB,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,SAAS,GAAG;YAChC,QAAQ,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QAChC;QACA,QAAQ,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IACpC;IAEA,8DAA8D;IAC9D,QAAQ,OAAO,CAAC,CAAC,aAAa;QAC5B,QAAQ,GAAG,CAAC,UAAU,YAAY,IAAI,CAAC,CAAC,GAAG,IACzC,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,OAAO;AACT;AAMO,SAAS,uBAAuB,YAAwC;IAC7E,MAAM,SAAwB,EAAE;IAChC,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM,GAAK,CAAC;YAC5E;YACA,OAAO;mBAAI;aAAM;QACnB,CAAC;IAED,sCAAsC;IACtC,MAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,KAAK,CAAC,MAAM,GAAG,GAAI;QACnD,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG;gBAC1B,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK;gBAC9B,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/optimizedHybridAlgorithm.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\nimport { PLAN_PRIORITY } from './planPrioritizer';\r\nimport { applyDiversityRules } from './diversityEngine';\r\n\r\n/**\r\n * Optimized Hybrid Algorithm for Exact Post Count\r\n * \r\n * Works with exactly the fetched posts (e.g., 10 posts) without losing any content\r\n * Strategy:\r\n * 1. Separate customer and business posts\r\n * 2. Apply plan prioritization to business posts\r\n * 3. Merge customer + business posts by timestamp\r\n * 4. Apply diversity rules\r\n * 5. Return all posts (no loss)\r\n */\r\n\r\nexport interface OptimizedHybridOptions {\r\n  enableDiversity?: boolean;\r\n  maintainChronologicalFlow?: boolean;\r\n}\r\n\r\n/**\r\n * Main optimized hybrid algorithm - processes exactly the fetched posts\r\n */\r\nexport function processOptimizedHybrid(\r\n  posts: UnifiedPost[],\r\n  options: OptimizedHybridOptions = {}\r\n): UnifiedPost[] {\r\n  const {\r\n    enableDiversity = true,\r\n    maintainChronologicalFlow = true\r\n  } = options;\r\n\r\n  if (posts.length === 0) return [];\r\n\r\n  // Separate customer and business posts\r\n  const customerPosts = posts.filter(post => post.post_source === 'customer');\r\n  const businessPosts = posts.filter(post => post.post_source === 'business');\r\n\r\n  // Process customer posts (maintain chronological order)\r\n  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);\r\n\r\n  // Process business posts (apply plan prioritization)\r\n  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);\r\n\r\n  // Merge both types\r\n  const mergedPosts = mergeOptimizedPosts(\r\n    processedCustomerPosts,\r\n    processedBusinessPosts,\r\n    maintainChronologicalFlow\r\n  );\r\n\r\n  // Apply diversity rules if enabled\r\n  return enableDiversity ? applyDiversityRules(mergedPosts) : mergedPosts;\r\n}\r\n\r\n/**\r\n * Process customer posts - simple chronological sort\r\n */\r\nfunction processCustomerPostsOptimized(customerPosts: UnifiedPost[]): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return [];\r\n\r\n  // Sort chronologically (latest first)\r\n  return customerPosts.sort((a, b) => \r\n    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n  );\r\n}\r\n\r\n/**\r\n * Process business posts - apply plan prioritization ONLY to latest post per business\r\n * Other posts from same business compete purely on timestamp\r\n */\r\nfunction processBusinessPostsOptimized(businessPosts: UnifiedPost[]): UnifiedPost[] {\r\n  if (businessPosts.length === 0) return [];\r\n\r\n  // Group posts by business (author_id)\r\n  const postsByBusiness = new Map<string, UnifiedPost[]>();\r\n  businessPosts.forEach(post => {\r\n    if (!postsByBusiness.has(post.author_id)) {\r\n      postsByBusiness.set(post.author_id, []);\r\n    }\r\n    postsByBusiness.get(post.author_id)!.push(post);\r\n  });\r\n\r\n  // Sort posts within each business by timestamp (latest first)\r\n  postsByBusiness.forEach((posts, businessId) => {\r\n    postsByBusiness.set(businessId, posts.sort((a, b) =>\r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n\r\n  // Separate latest posts (get plan priority) from other posts (time-based only)\r\n  const latestPostsPerBusiness: UnifiedPost[] = [];\r\n  const otherPostsFromBusinesses: UnifiedPost[] = [];\r\n\r\n  postsByBusiness.forEach((posts, _businessId) => {\r\n    if (posts.length > 0) {\r\n      // First post is latest (already sorted)\r\n      latestPostsPerBusiness.push(posts[0]);\r\n\r\n      // Rest are other posts from same business\r\n      if (posts.length > 1) {\r\n        otherPostsFromBusinesses.push(...posts.slice(1));\r\n      }\r\n    }\r\n  });\r\n\r\n  // Sort latest posts by plan priority + timestamp\r\n  const prioritizedLatestPosts = latestPostsPerBusiness.sort((a, b) => {\r\n    const planA = a.business_plan || 'free';\r\n    const planB = b.business_plan || 'free';\r\n\r\n    const priorityA = PLAN_PRIORITY[planA] || 1;\r\n    const priorityB = PLAN_PRIORITY[planB] || 1;\r\n\r\n    // Sort by plan priority first\r\n    if (priorityA !== priorityB) {\r\n      return priorityB - priorityA; // Higher priority first\r\n    }\r\n\r\n    // If same plan, sort by timestamp (latest first)\r\n    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n  });\r\n\r\n  // Sort other posts purely by timestamp (no plan priority)\r\n  const timeBasedOtherPosts = otherPostsFromBusinesses.sort((a, b) =>\r\n    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n  );\r\n\r\n  // Return prioritized latest posts first, then time-based other posts\r\n  return [...prioritizedLatestPosts, ...timeBasedOtherPosts];\r\n}\r\n\r\n/**\r\n * Merge customer and business posts with equal treatment\r\n * No priority between customer vs business - only plan priority within business posts\r\n */\r\nfunction mergeOptimizedPosts(\r\n  customerPosts: UnifiedPost[],\r\n  businessPosts: UnifiedPost[],\r\n  maintainChronologicalFlow: boolean\r\n): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return businessPosts;\r\n  if (businessPosts.length === 0) return customerPosts;\r\n\r\n  if (maintainChronologicalFlow) {\r\n    // Merge all posts by timestamp - equal treatment\r\n    return [...customerPosts, ...businessPosts]\r\n      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());\r\n  } else {\r\n    // Business posts first (due to plan prioritization), then customer posts\r\n    return [...businessPosts, ...customerPosts];\r\n  }\r\n}\r\n\r\n/**\r\n * Alternative approach: Intelligent interleaving\r\n * Ensures both customer and business posts get representation\r\n */\r\nexport function processOptimizedHybridWithInterleaving(\r\n  posts: UnifiedPost[],\r\n  options: OptimizedHybridOptions = {}\r\n): UnifiedPost[] {\r\n  const {\r\n    enableDiversity = true,\r\n    maintainChronologicalFlow = true\r\n  } = options;\r\n\r\n  if (posts.length === 0) return [];\r\n\r\n  // Separate and process posts\r\n  const customerPosts = posts.filter(post => post.post_source === 'customer');\r\n  const businessPosts = posts.filter(post => post.post_source === 'business');\r\n\r\n  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);\r\n  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);\r\n\r\n  // Intelligent interleaving\r\n  const interleavedPosts = intelligentInterleave(\r\n    processedCustomerPosts,\r\n    processedBusinessPosts,\r\n    maintainChronologicalFlow\r\n  );\r\n\r\n  // Apply diversity rules if enabled\r\n  return enableDiversity ? applyDiversityRules(interleavedPosts) : interleavedPosts;\r\n}\r\n\r\n/**\r\n * Intelligent interleaving of customer and business posts\r\n */\r\nfunction intelligentInterleave(\r\n  customerPosts: UnifiedPost[],\r\n  businessPosts: UnifiedPost[],\r\n  respectTimestamp: boolean\r\n): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return businessPosts;\r\n  if (businessPosts.length === 0) return customerPosts;\r\n\r\n  const result: UnifiedPost[] = [];\r\n  let customerIndex = 0;\r\n  let businessIndex = 0;\r\n\r\n  // Interleave posts while respecting timestamps if enabled\r\n  while (customerIndex < customerPosts.length || businessIndex < businessPosts.length) {\r\n    const customerPost = customerPosts[customerIndex];\r\n    const businessPost = businessPosts[businessIndex];\r\n\r\n    if (!customerPost && businessPost) {\r\n      // Only business posts left\r\n      result.push(businessPost);\r\n      businessIndex++;\r\n    } else if (customerPost && !businessPost) {\r\n      // Only customer posts left\r\n      result.push(customerPost);\r\n      customerIndex++;\r\n    } else if (customerPost && businessPost) {\r\n      // Both available - decide based on timestamp or alternating pattern\r\n      if (respectTimestamp) {\r\n        const customerTime = new Date(customerPost.created_at).getTime();\r\n        const businessTime = new Date(businessPost.created_at).getTime();\r\n        \r\n        if (businessTime >= customerTime) {\r\n          result.push(businessPost);\r\n          businessIndex++;\r\n        } else {\r\n          result.push(customerPost);\r\n          customerIndex++;\r\n        }\r\n      } else {\r\n        // Alternating pattern - business posts get slight preference due to plan prioritization\r\n        if (result.length % 2 === 0) {\r\n          result.push(businessPost);\r\n          businessIndex++;\r\n        } else {\r\n          result.push(customerPost);\r\n          customerIndex++;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Get statistics for the optimized algorithm\r\n */\r\nexport function getOptimizedAlgorithmStats(\r\n  originalPosts: UnifiedPost[],\r\n  processedPosts: UnifiedPost[]\r\n): {\r\n  originalCount: number;\r\n  processedCount: number;\r\n  customerPosts: number;\r\n  businessPosts: number;\r\n  planDistribution: Record<string, number>;\r\n  postsLost: number;\r\n  efficiency: number;\r\n} {\r\n  const planDistribution: Record<string, number> = {};\r\n  \r\n  const businessPosts = processedPosts.filter(p => p.post_source === 'business');\r\n  const customerPosts = processedPosts.filter(p => p.post_source === 'customer');\r\n  \r\n  businessPosts.forEach(post => {\r\n    const plan = post.business_plan || 'free';\r\n    planDistribution[plan] = (planDistribution[plan] || 0) + 1;\r\n  });\r\n\r\n  const postsLost = originalPosts.length - processedPosts.length;\r\n  const efficiency = processedPosts.length / originalPosts.length;\r\n\r\n  return {\r\n    originalCount: originalPosts.length,\r\n    processedCount: processedPosts.length,\r\n    customerPosts: customerPosts.length,\r\n    businessPosts: businessPosts.length,\r\n    planDistribution,\r\n    postsLost,\r\n    efficiency\r\n  };\r\n}\r\n\r\n/**\r\n * Validate that no posts are lost (should always be 100% with optimized algorithm)\r\n */\r\nexport function validateOptimizedAlgorithm(\r\n  originalPosts: UnifiedPost[],\r\n  processedPosts: UnifiedPost[]\r\n): {\r\n  isValid: boolean;\r\n  issues: string[];\r\n  efficiency: number;\r\n} {\r\n  const issues: string[] = [];\r\n  \r\n  if (originalPosts.length !== processedPosts.length) {\r\n    issues.push(`Post count mismatch: ${originalPosts.length} → ${processedPosts.length}`);\r\n  }\r\n\r\n  const originalIds = new Set(originalPosts.map(p => p.id));\r\n  const processedIds = new Set(processedPosts.map(p => p.id));\r\n  \r\n  const lostPosts: string[] = [];\r\n  originalIds.forEach(id => {\r\n    if (!processedIds.has(id)) {\r\n      lostPosts.push(id);\r\n    }\r\n  });\r\n\r\n  if (lostPosts.length > 0) {\r\n    issues.push(`Lost posts: ${lostPosts.join(', ')}`);\r\n  }\r\n\r\n  const efficiency = processedPosts.length / originalPosts.length;\r\n\r\n  return {\r\n    isValid: issues.length === 0,\r\n    issues,\r\n    efficiency\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAsBO,SAAS,uBACd,KAAoB,EACpB,UAAkC,CAAC,CAAC;IAEpC,MAAM,EACJ,kBAAkB,IAAI,EACtB,4BAA4B,IAAI,EACjC,GAAG;IAEJ,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;IAEjC,uCAAuC;IACvC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAChE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAEhE,wDAAwD;IACxD,MAAM,yBAAyB,8BAA8B;IAE7D,qDAAqD;IACrD,MAAM,yBAAyB,8BAA8B;IAE7D,mBAAmB;IACnB,MAAM,cAAc,oBAClB,wBACA,wBACA;IAGF,mCAAmC;IACnC,OAAO,kBAAkB,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;AAC9D;AAEA;;CAEC,GACD,SAAS,8BAA8B,aAA4B;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;IAEzC,sCAAsC;IACtC,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAC5B,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;AAErE;AAEA;;;CAGC,GACD,SAAS,8BAA8B,aAA4B;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;IAEzC,sCAAsC;IACtC,MAAM,kBAAkB,IAAI;IAC5B,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,SAAS,GAAG;YACxC,gBAAgB,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QACxC;QACA,gBAAgB,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IAC5C;IAEA,8DAA8D;IAC9D,gBAAgB,OAAO,CAAC,CAAC,OAAO;QAC9B,gBAAgB,GAAG,CAAC,YAAY,MAAM,IAAI,CAAC,CAAC,GAAG,IAC7C,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,+EAA+E;IAC/E,MAAM,yBAAwC,EAAE;IAChD,MAAM,2BAA0C,EAAE;IAElD,gBAAgB,OAAO,CAAC,CAAC,OAAO;QAC9B,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,wCAAwC;YACxC,uBAAuB,IAAI,CAAC,KAAK,CAAC,EAAE;YAEpC,0CAA0C;YAC1C,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,yBAAyB,IAAI,IAAI,MAAM,KAAK,CAAC;YAC/C;QACF;IACF;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,uBAAuB,IAAI,CAAC,CAAC,GAAG;QAC7D,MAAM,QAAQ,EAAE,aAAa,IAAI;QACjC,MAAM,QAAQ,EAAE,aAAa,IAAI;QAEjC,MAAM,YAAY,uIAAA,CAAA,gBAAa,CAAC,MAAM,IAAI;QAC1C,MAAM,YAAY,uIAAA,CAAA,gBAAa,CAAC,MAAM,IAAI;QAE1C,8BAA8B;QAC9B,IAAI,cAAc,WAAW;YAC3B,OAAO,YAAY,WAAW,wBAAwB;QACxD;QAEA,iDAAiD;QACjD,OAAO,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAC1E;IAEA,0DAA0D;IAC1D,MAAM,sBAAsB,yBAAyB,IAAI,CAAC,CAAC,GAAG,IAC5D,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAGnE,qEAAqE;IACrE,OAAO;WAAI;WAA2B;KAAoB;AAC5D;AAEA;;;CAGC,GACD,SAAS,oBACP,aAA4B,EAC5B,aAA4B,EAC5B,yBAAkC;IAElC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,IAAI,2BAA2B;QAC7B,iDAAiD;QACjD,OAAO;eAAI;eAAkB;SAAc,CACxC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IACrF,OAAO;QACL,yEAAyE;QACzE,OAAO;eAAI;eAAkB;SAAc;IAC7C;AACF;AAMO,SAAS,uCACd,KAAoB,EACpB,UAAkC,CAAC,CAAC;IAEpC,MAAM,EACJ,kBAAkB,IAAI,EACtB,4BAA4B,IAAI,EACjC,GAAG;IAEJ,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;IAEjC,6BAA6B;IAC7B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAChE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAEhE,MAAM,yBAAyB,8BAA8B;IAC7D,MAAM,yBAAyB,8BAA8B;IAE7D,2BAA2B;IAC3B,MAAM,mBAAmB,sBACvB,wBACA,wBACA;IAGF,mCAAmC;IACnC,OAAO,kBAAkB,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,oBAAoB;AACnE;AAEA;;CAEC,GACD,SAAS,sBACP,aAA4B,EAC5B,aAA4B,EAC5B,gBAAyB;IAEzB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,MAAM,SAAwB,EAAE;IAChC,IAAI,gBAAgB;IACpB,IAAI,gBAAgB;IAEpB,0DAA0D;IAC1D,MAAO,gBAAgB,cAAc,MAAM,IAAI,gBAAgB,cAAc,MAAM,CAAE;QACnF,MAAM,eAAe,aAAa,CAAC,cAAc;QACjD,MAAM,eAAe,aAAa,CAAC,cAAc;QAEjD,IAAI,CAAC,gBAAgB,cAAc;YACjC,2BAA2B;YAC3B,OAAO,IAAI,CAAC;YACZ;QACF,OAAO,IAAI,gBAAgB,CAAC,cAAc;YACxC,2BAA2B;YAC3B,OAAO,IAAI,CAAC;YACZ;QACF,OAAO,IAAI,gBAAgB,cAAc;YACvC,oEAAoE;YACpE,IAAI,kBAAkB;gBACpB,MAAM,eAAe,IAAI,KAAK,aAAa,UAAU,EAAE,OAAO;gBAC9D,MAAM,eAAe,IAAI,KAAK,aAAa,UAAU,EAAE,OAAO;gBAE9D,IAAI,gBAAgB,cAAc;oBAChC,OAAO,IAAI,CAAC;oBACZ;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF,OAAO;gBACL,wFAAwF;gBACxF,IAAI,OAAO,MAAM,GAAG,MAAM,GAAG;oBAC3B,OAAO,IAAI,CAAC;oBACZ;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,2BACd,aAA4B,EAC5B,cAA6B;IAU7B,MAAM,mBAA2C,CAAC;IAElD,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK;IACnE,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK;IAEnE,cAAc,OAAO,CAAC,CAAA;QACpB,MAAM,OAAO,KAAK,aAAa,IAAI;QACnC,gBAAgB,CAAC,KAAK,GAAG,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI;IAC3D;IAEA,MAAM,YAAY,cAAc,MAAM,GAAG,eAAe,MAAM;IAC9D,MAAM,aAAa,eAAe,MAAM,GAAG,cAAc,MAAM;IAE/D,OAAO;QACL,eAAe,cAAc,MAAM;QACnC,gBAAgB,eAAe,MAAM;QACrC,eAAe,cAAc,MAAM;QACnC,eAAe,cAAc,MAAM;QACnC;QACA;QACA;IACF;AACF;AAKO,SAAS,2BACd,aAA4B,EAC5B,cAA6B;IAM7B,MAAM,SAAmB,EAAE;IAE3B,IAAI,cAAc,MAAM,KAAK,eAAe,MAAM,EAAE;QAClD,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,cAAc,MAAM,CAAC,GAAG,EAAE,eAAe,MAAM,EAAE;IACvF;IAEA,MAAM,cAAc,IAAI,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IACvD,MAAM,eAAe,IAAI,IAAI,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAEzD,MAAM,YAAsB,EAAE;IAC9B,YAAY,OAAO,CAAC,CAAA;QAClB,IAAI,CAAC,aAAa,GAAG,CAAC,KAAK;YACzB,UAAU,IAAI,CAAC;QACjB;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,OAAO;IACnD;IAEA,MAAM,aAAa,eAAe,MAAM,GAAG,cAAc,MAAM;IAE/D,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1467, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/postCreationHandler.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Post Creation Handler - Manages immediate post visibility after creation\r\n * \r\n * Behavior:\r\n * 1. When user creates a post -> Show at top immediately (instant feedback)\r\n * 2. When user refreshes -> Apply normal algorithm (proper positioning)\r\n * \r\n * This provides excellent UX while maintaining algorithmic integrity\r\n */\r\n\r\nexport interface PostCreationState {\r\n  justCreatedPostId?: string;\r\n  sessionId?: string;\r\n  createdAt?: string;\r\n}\r\n\r\nexport interface FeedWithCreationState {\r\n  posts: UnifiedPost[];\r\n  hasJustCreatedPost: boolean;\r\n  justCreatedPost?: UnifiedPost;\r\n}\r\n\r\n/**\r\n * Handle feed display when user just created a post\r\n * Shows new post at top for immediate feedback\r\n */\r\nexport function handlePostCreationFeed(\r\n  algorithmicPosts: UnifiedPost[],\r\n  creationState: PostCreationState\r\n): FeedWithCreationState {\r\n  \r\n  if (!creationState.justCreatedPostId) {\r\n    // No recent post creation, return normal algorithmic feed\r\n    return {\r\n      posts: algorithmicPosts,\r\n      hasJustCreatedPost: false\r\n    };\r\n  }\r\n\r\n  // Find the just-created post in the algorithmic results\r\n  const justCreatedPost = algorithmicPosts.find(\r\n    post => post.id === creationState.justCreatedPostId\r\n  );\r\n\r\n  if (!justCreatedPost) {\r\n    // Post not found in current page, return normal feed\r\n    // (Post might be on a different page due to algorithm)\r\n    return {\r\n      posts: algorithmicPosts,\r\n      hasJustCreatedPost: false\r\n    };\r\n  }\r\n\r\n  // Remove the post from its algorithmic position\r\n  const otherPosts = algorithmicPosts.filter(\r\n    post => post.id !== creationState.justCreatedPostId\r\n  );\r\n\r\n  // Show just-created post at the top\r\n  return {\r\n    posts: [justCreatedPost, ...otherPosts],\r\n    hasJustCreatedPost: true,\r\n    justCreatedPost\r\n  };\r\n}\r\n\r\n/**\r\n * Create post creation state after successful post creation\r\n */\r\nexport function createPostCreationState(\r\n  postId: string,\r\n  sessionId?: string\r\n): PostCreationState {\r\n  return {\r\n    justCreatedPostId: postId,\r\n    sessionId: sessionId || generateSessionId(),\r\n    createdAt: new Date().toISOString()\r\n  };\r\n}\r\n\r\n/**\r\n * Check if post creation state is still valid (within session)\r\n */\r\nexport function isPostCreationStateValid(\r\n  creationState: PostCreationState,\r\n  currentSessionId?: string\r\n): boolean {\r\n  if (!creationState.justCreatedPostId) return false;\r\n  \r\n  // Check if it's the same session\r\n  if (creationState.sessionId && currentSessionId) {\r\n    return creationState.sessionId === currentSessionId;\r\n  }\r\n\r\n  // Check if creation was recent (within last 5 minutes as fallback)\r\n  if (creationState.createdAt) {\r\n    const createdTime = new Date(creationState.createdAt).getTime();\r\n    const now = new Date().getTime();\r\n    const fiveMinutes = 5 * 60 * 1000;\r\n    \r\n    return (now - createdTime) < fiveMinutes;\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\n/**\r\n * Clear post creation state (call on refresh or navigation)\r\n */\r\nexport function clearPostCreationState(): PostCreationState {\r\n  return {};\r\n}\r\n\r\n/**\r\n * Generate a simple session ID for tracking\r\n */\r\nfunction generateSessionId(): string {\r\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n}\r\n\r\n/**\r\n * Enhanced feed response that includes creation state information\r\n */\r\nexport interface EnhancedFeedResponse {\r\n  success: boolean;\r\n  message: string;\r\n  error?: string;\r\n  data?: {\r\n    items: UnifiedPost[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    hasJustCreatedPost: boolean;\r\n    justCreatedPost?: UnifiedPost;\r\n    creationState?: PostCreationState;\r\n  };\r\n}\r\n\r\n/**\r\n * Process feed with post creation handling\r\n */\r\nexport function processFeedWithCreationHandling(\r\n  algorithmicPosts: UnifiedPost[],\r\n  totalCount: number,\r\n  hasMore: boolean,\r\n  creationState?: PostCreationState\r\n): EnhancedFeedResponse {\r\n\r\n  if (!creationState || !creationState.justCreatedPostId) {\r\n    // No post creation state, return normal feed\r\n    return {\r\n      success: true,\r\n      message: 'Posts fetched successfully',\r\n      data: {\r\n        items: algorithmicPosts,\r\n        totalCount,\r\n        hasMore,\r\n        hasJustCreatedPost: false\r\n      }\r\n    };\r\n  }\r\n\r\n  // Handle post creation display\r\n  const feedWithCreation = handlePostCreationFeed(algorithmicPosts, creationState);\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Posts fetched successfully',\r\n    data: {\r\n      items: feedWithCreation.posts,\r\n      totalCount,\r\n      hasMore,\r\n      hasJustCreatedPost: feedWithCreation.hasJustCreatedPost,\r\n      justCreatedPost: feedWithCreation.justCreatedPost,\r\n      creationState\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Client-side helper to manage post creation state in localStorage/sessionStorage\r\n */\r\nexport const PostCreationStateManager = {\r\n  \r\n  /**\r\n   * Save post creation state to session storage\r\n   */\r\n  save(state: PostCreationState): void {\r\n    if (typeof window !== 'undefined') {\r\n      sessionStorage.setItem('post_creation_state', JSON.stringify(state));\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Load post creation state from session storage\r\n   */\r\n  load(): PostCreationState {\r\n    if (typeof window !== 'undefined') {\r\n      const stored = sessionStorage.getItem('post_creation_state');\r\n      if (stored) {\r\n        try {\r\n          return JSON.parse(stored);\r\n        } catch (e) {\r\n          console.warn('Failed to parse post creation state:', e);\r\n        }\r\n      }\r\n    }\r\n    return {};\r\n  },\r\n\r\n  /**\r\n   * Clear post creation state from session storage\r\n   */\r\n  clear(): void {\r\n    if (typeof window !== 'undefined') {\r\n      sessionStorage.removeItem('post_creation_state');\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Check if current state is valid and clear if not\r\n   */\r\n  validateAndClean(): PostCreationState {\r\n    const state = this.load();\r\n    const currentSessionId = this.getCurrentSessionId();\r\n    \r\n    if (!isPostCreationStateValid(state, currentSessionId)) {\r\n      this.clear();\r\n      return {};\r\n    }\r\n    \r\n    return state;\r\n  },\r\n\r\n  /**\r\n   * Get or create current session ID\r\n   */\r\n  getCurrentSessionId(): string {\r\n    if (typeof window !== 'undefined') {\r\n      let sessionId = sessionStorage.getItem('current_session_id');\r\n      if (!sessionId) {\r\n        sessionId = generateSessionId();\r\n        sessionStorage.setItem('current_session_id', sessionId);\r\n      }\r\n      return sessionId;\r\n    }\r\n    return generateSessionId();\r\n  }\r\n};\r\n\r\n/**\r\n * Hook-like function for React components to manage post creation state\r\n */\r\nexport function usePostCreationState() {\r\n  const load = () => PostCreationStateManager.validateAndClean();\r\n  const save = (state: PostCreationState) => PostCreationStateManager.save(state);\r\n  const clear = () => PostCreationStateManager.clear();\r\n  \r\n  return { load, save, clear };\r\n}\r\n\r\n/**\r\n * Utility to mark a post as just created (call after successful post creation)\r\n */\r\nexport function markPostAsJustCreated(postId: string): void {\r\n  const state = createPostCreationState(\r\n    postId, \r\n    PostCreationStateManager.getCurrentSessionId()\r\n  );\r\n  PostCreationStateManager.save(state);\r\n}\r\n\r\n/**\r\n * Utility to check if we should show the \"just posted\" indicator\r\n */\r\nexport function shouldShowJustPostedIndicator(\r\n  post: UnifiedPost, \r\n  creationState?: PostCreationState\r\n): boolean {\r\n  if (!creationState || !creationState.justCreatedPostId) return false;\r\n  return post.id === creationState.justCreatedPostId;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AA4BO,SAAS,uBACd,gBAA+B,EAC/B,aAAgC;IAGhC,IAAI,CAAC,cAAc,iBAAiB,EAAE;QACpC,0DAA0D;QAC1D,OAAO;YACL,OAAO;YACP,oBAAoB;QACtB;IACF;IAEA,wDAAwD;IACxD,MAAM,kBAAkB,iBAAiB,IAAI,CAC3C,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,iBAAiB;IAGrD,IAAI,CAAC,iBAAiB;QACpB,qDAAqD;QACrD,uDAAuD;QACvD,OAAO;YACL,OAAO;YACP,oBAAoB;QACtB;IACF;IAEA,gDAAgD;IAChD,MAAM,aAAa,iBAAiB,MAAM,CACxC,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,iBAAiB;IAGrD,oCAAoC;IACpC,OAAO;QACL,OAAO;YAAC;eAAoB;SAAW;QACvC,oBAAoB;QACpB;IACF;AACF;AAKO,SAAS,wBACd,MAAc,EACd,SAAkB;IAElB,OAAO;QACL,mBAAmB;QACnB,WAAW,aAAa;QACxB,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAKO,SAAS,yBACd,aAAgC,EAChC,gBAAyB;IAEzB,IAAI,CAAC,cAAc,iBAAiB,EAAE,OAAO;IAE7C,iCAAiC;IACjC,IAAI,cAAc,SAAS,IAAI,kBAAkB;QAC/C,OAAO,cAAc,SAAS,KAAK;IACrC;IAEA,mEAAmE;IACnE,IAAI,cAAc,SAAS,EAAE;QAC3B,MAAM,cAAc,IAAI,KAAK,cAAc,SAAS,EAAE,OAAO;QAC7D,MAAM,MAAM,IAAI,OAAO,OAAO;QAC9B,MAAM,cAAc,IAAI,KAAK;QAE7B,OAAO,AAAC,MAAM,cAAe;IAC/B;IAEA,OAAO;AACT;AAKO,SAAS;IACd,OAAO,CAAC;AACV;AAEA;;CAEC,GACD,SAAS;IACP,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAsBO,SAAS,gCACd,gBAA+B,EAC/B,UAAkB,EAClB,OAAgB,EAChB,aAAiC;IAGjC,IAAI,CAAC,iBAAiB,CAAC,cAAc,iBAAiB,EAAE;QACtD,6CAA6C;QAC7C,OAAO;YACL,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP;gBACA;gBACA,oBAAoB;YACtB;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,uBAAuB,kBAAkB;IAElE,OAAO;QACL,SAAS;QACT,SAAS;QACT,MAAM;YACJ,OAAO,iBAAiB,KAAK;YAC7B;YACA;YACA,oBAAoB,iBAAiB,kBAAkB;YACvD,iBAAiB,iBAAiB,eAAe;YACjD;QACF;IACF;AACF;AAKO,MAAM,2BAA2B;IAEtC;;GAEC,GACD,MAAK,KAAwB;QAC3B,uCAAmC;;QAEnC;IACF;IAEA;;GAEC,GACD;QACE,uCAAmC;;QASnC;QACA,OAAO,CAAC;IACV;IAEA;;GAEC,GACD;QACE,uCAAmC;;QAEnC;IACF;IAEA;;GAEC,GACD;QACE,MAAM,QAAQ,IAAI,CAAC,IAAI;QACvB,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;QAEjD,IAAI,CAAC,yBAAyB,OAAO,mBAAmB;YACtD,IAAI,CAAC,KAAK;YACV,OAAO,CAAC;QACV;QAEA,OAAO;IACT;IAEA;;GAEC,GACD;QACE,uCAAmC;;QAOnC;QACA,OAAO;IACT;AACF;AAKO,SAAS;IACd,MAAM,OAAO,IAAM,yBAAyB,gBAAgB;IAC5D,MAAM,OAAO,CAAC,QAA6B,yBAAyB,IAAI,CAAC;IACzE,MAAM,QAAQ,IAAM,yBAAyB,KAAK;IAElD,OAAO;QAAE;QAAM;QAAM;IAAM;AAC7B;AAKO,SAAS,sBAAsB,MAAc;IAClD,MAAM,QAAQ,wBACZ,QACA,yBAAyB,mBAAmB;IAE9C,yBAAyB,IAAI,CAAC;AAChC;AAKO,SAAS,8BACd,IAAiB,EACjB,aAAiC;IAEjC,IAAI,CAAC,iBAAiB,CAAC,cAAc,iBAAiB,EAAE,OAAO;IAC/D,OAAO,KAAK,EAAE,KAAK,cAAc,iBAAiB;AACpD", "debugId": null}}, {"offset": {"line": 1634, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/unifiedFeed.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/client';\nimport { TABLES, COLUMNS } from \"../../supabase/constants\";\nimport { Database, Tables } from \"../../../types/supabase\";\nimport { FeedQueryParams } from '@/lib/types/posts';\nimport { processOptimizedHybrid } from '@/lib/utils/feed/optimizedHybridAlgorithm';\nimport {\n  processFeedWithCreationHandling,\n  PostCreationState,\n  EnhancedFeedResponse\n} from '@/lib/utils/feed/postCreationHandler';\n\n// getUserProfile\nasync function getUserProfile(userId: string) {\n  const supabase = createClient();\n  try {\n    const { data, error } = await supabase\n      .from(TABLES.CUSTOMER_PROFILES)\n      .select(\"*\")\n      .eq(COLUMNS.ID, userId)\n      .single();\n\n    if (error) {\n      console.error(\"Error fetching user profile:\", error.message);\n      return { data: null, error: error.message };\n    }\n    return { data, error: null };\n  } catch (err) {\n    console.error(\"Unexpected error fetching user profile:\", err);\n    return { data: null, error: \"An unexpected error occurred.\" };\n  }\n}\n\nexport interface UnifiedPost {\n  id: string;\n  post_source: 'business' | 'customer';\n  author_id: string;\n  content: string;\n  image_url: string | null;\n  created_at: string;\n  updated_at: string;\n  city_slug: string | null;\n  state_slug: string | null;\n  locality_slug: string | null;\n  pincode: string | null;\n  product_ids: string[];\n  mentioned_business_ids: string[];\n  author_name: string | null;\n  author_avatar: string | null;\n  business_slug: string | null; // Business slug for business posts, null for customer posts\n  phone: string | null; // Phone number for business posts, null for customer posts\n  whatsapp_number: string | null; // WhatsApp number for business posts, null for customer posts\n  business_plan: string | null; // Plan for business posts, null for customer posts\n}\n\nexport interface UnifiedFeedResponse {\n  success: boolean;\n  message: string;\n  error?: string;\n  data?: {\n    items: UnifiedPost[];\n    totalCount: number;\n    hasMore: boolean;\n  };\n}\n\n/**\n * Get unified feed posts (business + customer posts) with proper pagination\n * Supports post creation state for immediate feedback when user creates a post\n */\nexport async function getUnifiedFeedPosts(\n  params: FeedQueryParams,\n  creationState?: PostCreationState\n): Promise<EnhancedFeedResponse> {\n  const supabase = createClient();\n  const {\n    filter = 'smart',\n    page = 1,\n    limit = 10,\n    city_slug,\n    state_slug,\n    locality_slug,\n    pincode\n  } = params;\n\n  try {\n    // Get current user for smart and subscribed filters\n    const { data: { user } } = await supabase.auth.getUser();\n\n    // Build base query\n    let query = supabase\n      .from('unified_posts')\n      .select('*', { count: 'exact' }) as any;\n\n    // Apply filters based on filter type\n    switch (filter) {\n      case 'smart':\n        if (user) {\n          // Get user's subscribed businesses for smart feed\n          const { data: subscriptions } = await supabase\n            .from('subscriptions')\n            .select('business_profile_id')\n            .eq(COLUMNS.USER_ID, user.id);\n\n          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];\n\n          // Try to get user's location from both customer and business profiles\n          const [customerProfile, businessProfile] = await Promise.all([\n            getUserProfile(user.id),\n            supabase\n              .from(TABLES.BUSINESS_PROFILES)\n              .select(`${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}, ${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}`)\n              .eq(COLUMNS.ID, user.id)\n              .single()\n          ]);\n\n          // Use whichever profile exists\n          const userProfile = customerProfile.data || businessProfile.data;\n\n          // Build smart feed conditions\n          const conditions = [];\n\n          // Subscribed businesses\n          if (subscribedBusinessIds.length > 0) {\n            conditions.push(`and(post_source.eq.business,author_id.in.(${subscribedBusinessIds.join(',')}))`);\n          }\n\n          // User's own posts (check both customer and business posts)\n          conditions.push(`and(post_source.eq.customer,author_id.eq.${user.id})`);\n          conditions.push(`and(post_source.eq.business,author_id.eq.${user.id})`);\n\n          // Local posts based on user location\n          if (userProfile?.locality_slug) {\n            conditions.push(`${COLUMNS.LOCALITY_SLUG}.eq.${userProfile.locality_slug}`);\n          }\n          if (userProfile?.pincode) {\n            conditions.push(`${COLUMNS.PINCODE}.eq.${userProfile.pincode}`);\n          }\n          if (userProfile?.city_slug) {\n            conditions.push(`${COLUMNS.CITY_SLUG}.eq.${userProfile.city_slug}`);\n          }\n\n          if (conditions.length > 0) {\n            query = query.or(conditions.join(','));\n          }\n        }\n        break;\n\n      case 'subscribed':\n        if (user) {\n          const { data: subscriptions } = await supabase\n            .from('subscriptions')\n            .select('business_profile_id')\n            .eq('user_id', user.id);\n\n          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];\n\n          if (subscribedBusinessIds.length > 0) {\n            query = query\n              .eq('post_source', 'business')\n              .in('author_id', subscribedBusinessIds);\n          } else {\n            // No subscriptions, return empty result\n            return {\n              success: true,\n              message: 'No subscribed businesses found',\n              data: {\n                items: [],\n                totalCount: 0,\n                hasMore: false,\n                hasJustCreatedPost: false\n              }\n            };\n          }\n        }\n        break;\n\n      case 'locality':\n        if (locality_slug) {\n          query = query.eq('locality_slug', locality_slug);\n        } else if (user) {\n          // If no locality_slug provided, get user's locality from their profile\n          const [customerProfile, businessProfile] = await Promise.all([\n            supabase.from(TABLES.CUSTOMER_PROFILES).select(COLUMNS.LOCALITY_SLUG).eq(COLUMNS.ID, user.id).single(),\n            supabase.from(TABLES.BUSINESS_PROFILES).select(COLUMNS.LOCALITY_SLUG).eq(COLUMNS.ID, user.id).single()\n          ]);\n          const userLocality = customerProfile.data?.locality_slug || businessProfile.data?.locality_slug;\n          if (userLocality) {\n            query = query.eq('locality_slug', userLocality);\n          }\n        }\n        break;\n\n      case 'pincode':\n        if (pincode) {\n          query = query.eq('pincode', pincode);\n        } else if (user) {\n          // If no pincode provided, get user's pincode from their profile\n          const [customerProfile, businessProfile] = await Promise.all([\n            supabase.from(TABLES.CUSTOMER_PROFILES).select(COLUMNS.PINCODE).eq(COLUMNS.ID, user.id).single(),\n            supabase.from(TABLES.BUSINESS_PROFILES).select(COLUMNS.PINCODE).eq(COLUMNS.ID, user.id).single()\n          ]);\n          const userPincode = customerProfile.data?.pincode || businessProfile.data?.pincode;\n          if (userPincode) {\n            query = query.eq('pincode', userPincode);\n          }\n        }\n        break;\n\n      case 'city':\n        if (city_slug) {\n          query = query.eq('city_slug', city_slug);\n        } else if (user) {\n          // If no city_slug provided, get user's city from their profile\n          const [customerProfile, businessProfile] = await Promise.all([\n            supabase.from(TABLES.CUSTOMER_PROFILES).select(COLUMNS.CITY_SLUG).eq(COLUMNS.ID, user.id).single(),\n            supabase.from(TABLES.BUSINESS_PROFILES).select(COLUMNS.CITY_SLUG).eq(COLUMNS.ID, user.id).single()\n          ]);\n          const userCity = customerProfile.data?.city_slug || businessProfile.data?.city_slug;\n          if (userCity) {\n            query = query.eq('city_slug', userCity);\n          }\n        }\n        break;\n\n      case 'state':\n        if (state_slug) {\n          query = query.eq('state_slug', state_slug);\n        } else if (user) {\n          // If no state_slug provided, get user's state from their profile\n          const [customerProfile, businessProfile] = await Promise.all([\n            supabase.from(TABLES.CUSTOMER_PROFILES).select(COLUMNS.STATE_SLUG).eq(COLUMNS.ID, user.id).single(),\n            supabase.from(TABLES.BUSINESS_PROFILES).select(COLUMNS.STATE_SLUG).eq(COLUMNS.ID, user.id).single()\n          ]);\n          const userState = customerProfile.data?.state_slug || businessProfile.data?.state_slug;\n          if (userState) {\n            query = query.eq('state_slug', userState);\n          }\n        }\n        break;\n\n      case 'all':\n        // No additional filters for 'all'\n        break;\n    }\n\n    // Fetch exactly the target number of posts to prevent post loss\n    // Algorithm will arrange these posts optimally without losing any content\n    const from = (page - 1) * limit; // Standard pagination\n    const to = from + limit - 1;\n\n    // Execute query with chronological ordering (prioritization applied client-side)\n    const { data, error, count } = await query\n      .order('created_at', { ascending: false })\n      .range(from, to);\n\n    if (error) {\n      console.error('Error fetching unified feed posts:', error);\n      return {\n        success: false,\n        message: 'Failed to fetch posts',\n        error: error.message\n      };\n    }\n\n    // Apply optimized hybrid algorithm to ALL feed types\n    // Processes exactly the fetched posts without losing any content\n    // Business posts get plan prioritization, customer posts maintain chronological order\n    // Works with location filters (locality, pincode, city, state, all)\n    const prioritizedData = data ? processOptimizedHybrid(data, {\n      enableDiversity: true,\n      maintainChronologicalFlow: true\n    }) : [];\n\n    const totalCount = count || 0;\n    // Standard pagination logic - no posts lost\n    const hasMore = prioritizedData.length === limit && (from + limit) < totalCount;\n\n    // Handle post creation state for immediate feedback\n    return processFeedWithCreationHandling(\n      prioritizedData,\n      totalCount,\n      hasMore,\n      creationState\n    );\n\n  } catch (error) {\n    console.error('Unexpected error in getUnifiedFeedPosts:', error);\n    return {\n      success: false,\n      message: 'An unexpected error occurred',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\r\n}\r\n\r\n/**\r\n * Get unified feed posts with author information populated\r\n * Author information is now included directly in the unified_posts view\r\n */\r\nexport async function getUnifiedFeedPostsWithAuthors(\r\n  params: FeedQueryParams,\r\n  creationState?: PostCreationState\r\n): Promise<EnhancedFeedResponse> {\r\n  // Since author information is now included in the unified_posts view,\r\n  // we can just return the result from getUnifiedFeedPosts directly\r\n  return await getUnifiedFeedPosts(params, creationState);\r\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAGA;AACA;;;;;AAMA,iBAAiB;AACjB,eAAe,eAAe,MAAc;IAC1C,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAC5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,KACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,QACf,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC,MAAM,OAAO;YAC3D,OAAO;gBAAE,MAAM;gBAAM,OAAO,MAAM,OAAO;YAAC;QAC5C;QACA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,MAAM;YAAM,OAAO;QAAgC;IAC9D;AACF;AAuCO,eAAe,oBACpB,MAAuB,EACvB,aAAiC;IAEjC,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EACJ,SAAS,OAAO,EAChB,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,SAAS,EACT,UAAU,EACV,aAAa,EACb,OAAO,EACR,GAAG;IAEJ,IAAI;QACF,oDAAoD;QACpD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEtD,mBAAmB;QACnB,IAAI,QAAQ,SACT,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ;QAEhC,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,IAAI,MAAM;oBACR,kDAAkD;oBAClD,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,iBACL,MAAM,CAAC,uBACP,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,KAAK,EAAE;oBAE9B,MAAM,wBAAwB,eAAe,IAAI,CAAA,IAAK,EAAE,mBAAmB,KAAK,EAAE;oBAElF,sEAAsE;oBACtE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,eAAe,KAAK,EAAE;wBACtB,SACG,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,EAAE,EAAE,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,EAClG,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KAAK,EAAE,EACtB,MAAM;qBACV;oBAED,+BAA+B;oBAC/B,MAAM,cAAc,gBAAgB,IAAI,IAAI,gBAAgB,IAAI;oBAEhE,8BAA8B;oBAC9B,MAAM,aAAa,EAAE;oBAErB,wBAAwB;oBACxB,IAAI,sBAAsB,MAAM,GAAG,GAAG;wBACpC,WAAW,IAAI,CAAC,CAAC,0CAA0C,EAAE,sBAAsB,IAAI,CAAC,KAAK,EAAE,CAAC;oBAClG;oBAEA,4DAA4D;oBAC5D,WAAW,IAAI,CAAC,CAAC,yCAAyC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBACtE,WAAW,IAAI,CAAC,CAAC,yCAAyC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBAEtE,qCAAqC;oBACrC,IAAI,aAAa,eAAe;wBAC9B,WAAW,IAAI,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,aAAa,EAAE;oBAC5E;oBACA,IAAI,aAAa,SAAS;wBACxB,WAAW,IAAI,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,OAAO,EAAE;oBAChE;oBACA,IAAI,aAAa,WAAW;wBAC1B,WAAW,IAAI,CAAC,GAAG,4HAAA,CAAA,UAAO,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,SAAS,EAAE;oBACpE;oBAEA,IAAI,WAAW,MAAM,GAAG,GAAG;wBACzB,QAAQ,MAAM,EAAE,CAAC,WAAW,IAAI,CAAC;oBACnC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,MAAM;oBACR,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,iBACL,MAAM,CAAC,uBACP,EAAE,CAAC,WAAW,KAAK,EAAE;oBAExB,MAAM,wBAAwB,eAAe,IAAI,CAAA,IAAK,EAAE,mBAAmB,KAAK,EAAE;oBAElF,IAAI,sBAAsB,MAAM,GAAG,GAAG;wBACpC,QAAQ,MACL,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,aAAa;oBACrB,OAAO;wBACL,wCAAwC;wBACxC,OAAO;4BACL,SAAS;4BACT,SAAS;4BACT,MAAM;gCACJ,OAAO,EAAE;gCACT,YAAY;gCACZ,SAAS;gCACT,oBAAoB;4BACtB;wBACF;oBACF;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,eAAe;oBACjB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;gBACpC,OAAO,IAAI,MAAM;oBACf,uEAAuE;oBACvE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM;wBACpG,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,aAAa,EAAE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM;qBACrG;oBACD,MAAM,eAAe,gBAAgB,IAAI,EAAE,iBAAiB,gBAAgB,IAAI,EAAE;oBAClF,IAAI,cAAc;wBAChB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;oBACpC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS;oBACX,QAAQ,MAAM,EAAE,CAAC,WAAW;gBAC9B,OAAO,IAAI,MAAM;oBACf,gEAAgE;oBAChE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM;wBAC9F,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,OAAO,EAAE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM;qBAC/F;oBACD,MAAM,cAAc,gBAAgB,IAAI,EAAE,WAAW,gBAAgB,IAAI,EAAE;oBAC3E,IAAI,aAAa;wBACf,QAAQ,MAAM,EAAE,CAAC,WAAW;oBAC9B;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,WAAW;oBACb,QAAQ,MAAM,EAAE,CAAC,aAAa;gBAChC,OAAO,IAAI,MAAM;oBACf,+DAA+D;oBAC/D,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,SAAS,EAAE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM;wBAChG,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,SAAS,EAAE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM;qBACjG;oBACD,MAAM,WAAW,gBAAgB,IAAI,EAAE,aAAa,gBAAgB,IAAI,EAAE;oBAC1E,IAAI,UAAU;wBACZ,QAAQ,MAAM,EAAE,CAAC,aAAa;oBAChC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,YAAY;oBACd,QAAQ,MAAM,EAAE,CAAC,cAAc;gBACjC,OAAO,IAAI,MAAM;oBACf,iEAAiE;oBACjE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM;wBACjG,SAAS,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,UAAU,EAAE,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM;qBAClG;oBACD,MAAM,YAAY,gBAAgB,IAAI,EAAE,cAAc,gBAAgB,IAAI,EAAE;oBAC5E,IAAI,WAAW;wBACb,QAAQ,MAAM,EAAE,CAAC,cAAc;oBACjC;gBACF;gBACA;YAEF,KAAK;gBAEH;QACJ;QAEA,gEAAgE;QAChE,0EAA0E;QAC1E,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,sBAAsB;QACvD,MAAM,KAAK,OAAO,QAAQ;QAE1B,iFAAiF;QACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,MAClC,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,MAAM;QAEf,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,MAAM,OAAO;YACtB;QACF;QAEA,qDAAqD;QACrD,iEAAiE;QACjE,sFAAsF;QACtF,oEAAoE;QACpE,MAAM,kBAAkB,OAAO,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;YAC1D,iBAAiB;YACjB,2BAA2B;QAC7B,KAAK,EAAE;QAEP,MAAM,aAAa,SAAS;QAC5B,4CAA4C;QAC5C,MAAM,UAAU,gBAAgB,MAAM,KAAK,SAAS,AAAC,OAAO,QAAS;QAErE,oDAAoD;QACpD,OAAO,CAAA,GAAA,2IAAA,CAAA,kCAA+B,AAAD,EACnC,iBACA,YACA,SACA;IAGJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAMO,eAAe,+BACpB,MAAuB,EACvB,aAAiC;IAEjC,sEAAsE;IACtE,kEAAkE;IAClE,OAAO,MAAM,oBAAoB,QAAQ;AAC3C", "debugId": null}}, {"offset": {"line": 1851, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/page.tsx"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { redirect } from 'next/navigation';\r\nimport { Metadata } from 'next';\r\nimport ModernBusinessFeedList from '@/components/feed/ModernBusinessFeedList';\r\nimport { getUnifiedFeedPostsWithAuthors } from '@/lib/actions/posts/unifiedFeed';\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Feed\",\r\n  description: \"View your business feed and stay updated\",\r\n};\r\n\r\nexport default async function BusinessDashboardPage() {\r\n  const supabase = await createClient();\r\n\r\n  // Check if user is authenticated\r\n  const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    redirect('/login?message=Please log in to view your dashboard');\r\n  }\r\n\r\n  // Get the user's business profile\r\n  const { data: businessProfile, error: profileError } = await supabase\r\n    .from('business_profiles')\r\n    .select('city_slug, state_slug, locality_slug, pincode, business_name')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError) {\r\n    // Handle profile error silently or redirect if needed\r\n  }\r\n\r\n  // Get initial posts using the unified feed algorithm\r\n  const initialFeedResult = await getUnifiedFeedPostsWithAuthors({\r\n    filter: 'smart',\r\n    page: 1,\r\n    limit: 10,\r\n    city_slug: businessProfile?.city_slug || undefined,\r\n    state_slug: businessProfile?.state_slug || undefined,\r\n    locality_slug: businessProfile?.locality_slug || undefined,\r\n    pincode: businessProfile?.pincode || undefined\r\n  });\r\n\r\n  const posts = initialFeedResult.success ? initialFeedResult.data?.items || [] : [];\r\n  const hasMore = initialFeedResult.success ? initialFeedResult.data?.hasMore || false : false;\r\n\r\n  if (!initialFeedResult.success) {\r\n    console.error('Error fetching initial posts:', initialFeedResult.error);\r\n  }\r\n\r\n  return (\r\n    <ModernBusinessFeedList\r\n      initialPosts={posts}\r\n      initialTotalCount={0} // Not needed for infinite scroll\r\n      initialHasMore={hasMore}\r\n      initialFilter=\"smart\"\r\n      citySlug={businessProfile?.city_slug || undefined}\r\n      stateSlug={businessProfile?.state_slug || undefined}\r\n      localitySlug={businessProfile?.locality_slug || undefined}\r\n      pincode={businessProfile?.pincode || undefined}\r\n      businessName={businessProfile?.business_name || 'Business Owner'}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAEA;AACA;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,iCAAiC;IACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,kCAAkC;IAClC,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,qBACL,MAAM,CAAC,gEACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,cAAc;IAChB,sDAAsD;IACxD;IAEA,qDAAqD;IACrD,MAAM,oBAAoB,MAAM,CAAA,GAAA,sIAAA,CAAA,iCAA8B,AAAD,EAAE;QAC7D,QAAQ;QACR,MAAM;QACN,OAAO;QACP,WAAW,iBAAiB,aAAa;QACzC,YAAY,iBAAiB,cAAc;QAC3C,eAAe,iBAAiB,iBAAiB;QACjD,SAAS,iBAAiB,WAAW;IACvC;IAEA,MAAM,QAAQ,kBAAkB,OAAO,GAAG,kBAAkB,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE;IAClF,MAAM,UAAU,kBAAkB,OAAO,GAAG,kBAAkB,IAAI,EAAE,WAAW,QAAQ;IAEvF,IAAI,CAAC,kBAAkB,OAAO,EAAE;QAC9B,QAAQ,KAAK,CAAC,iCAAiC,kBAAkB,KAAK;IACxE;IAEA,qBACE,8OAAC,6IAAA,CAAA,UAAsB;QACrB,cAAc;QACd,mBAAmB;QACnB,gBAAgB;QAChB,eAAc;QACd,UAAU,iBAAiB,aAAa;QACxC,WAAW,iBAAiB,cAAc;QAC1C,cAAc,iBAAiB,iBAAiB;QAChD,SAAS,iBAAiB,WAAW;QACrC,cAAc,iBAAiB,iBAAiB;;;;;;AAGtD", "debugId": null}}]}