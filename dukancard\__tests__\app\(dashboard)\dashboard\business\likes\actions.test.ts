import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { getBusinessLikes, getBusinessLikesReceived } from '@/app/(dashboard)/dashboard/business/likes/actions';
import { socialService } from '@/lib/services/socialService';
import { createClient } from '@/utils/supabase/server';

// Mock the social service
jest.mock('@/lib/services/socialService', () => ({
  socialService: {
    likesService: {
      fetchLikes: jest.fn(),
      fetchBusinessLikesReceived: jest.fn(),
    },
  },
}));

// Mock the Supabase client
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));

describe('Business Likes Actions', () => {
  let mockSupabase: any;

  beforeEach(() => {
    mockSupabase = {
      auth: {
        getUser: jest.fn(),
      },
    };

    (createClient as jest.Mock).mockResolvedValue(mockSupabase);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getBusinessLikes', () => {
    it('should fetch business likes successfully', async () => {
      const mockUser = { id: 'user123' };
      const mockLikesData = {
        items: [
          {
            id: '1',
            business_profiles: {
              id: 'business1',
              business_name: 'Test Business',
              business_slug: 'test-business',
            },
          },
        ],
        totalCount: 1,
        hasMore: false,
        currentPage: 1,
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      jest.mocked(socialService.likesService.fetchLikes).mockResolvedValue(mockLikesData);

      const result = await getBusinessLikes({
        page: 1,
        pageSize: 10,
        sortBy: 'newest',
        searchTerm: '',
      });

      expect(result.likes).toEqual(mockLikesData.items);
      expect(result.count).toBe(mockLikesData.totalCount);
      expect(result.hasMore).toBe(mockLikesData.hasMore);
      expect(result.error).toBeNull();
      expect(socialService.likesService.fetchLikes).toHaveBeenCalledWith(
        'user123',
        1,
        10,
        'newest',
        ''
      );
    });

    it('should handle authentication error', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Not authenticated' },
      });

      const result = await getBusinessLikes({
        page: 1,
        pageSize: 10,
        sortBy: 'newest',
        searchTerm: '',
      });

      expect(result.likes).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.hasMore).toBe(false);
      expect(result.error).toBe('Not authenticated');
    });

    it('should handle service error', async () => {
      const mockUser = { id: 'user123' };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      jest.mocked(socialService.likesService.fetchLikes).mockRejectedValue(
        new Error('Service error')
      );

      const result = await getBusinessLikes({
        page: 1,
        pageSize: 10,
        sortBy: 'newest',
        searchTerm: '',
      });

      expect(result.likes).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.hasMore).toBe(false);
      expect(result.error).toBe('Service error');
    });

    it('should handle missing user', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const result = await getBusinessLikes({
        page: 1,
        pageSize: 10,
        sortBy: 'newest',
        searchTerm: '',
      });

      expect(result.likes).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.hasMore).toBe(false);
      expect(result.error).toBe('User not found');
    });

    it('should handle search term', async () => {
      const mockUser = { id: 'user123' };
      const mockLikesData = {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: 1,
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      jest.mocked(socialService.likesService.fetchLikes).mockResolvedValue(mockLikesData);

      await getBusinessLikes({
        page: 1,
        pageSize: 10,
        sortBy: 'newest',
        searchTerm: 'test business',
      });

      expect(socialService.likesService.fetchLikes).toHaveBeenCalledWith(
        'user123',
        1,
        10,
        'newest',
        'test business'
      );
    });
  });

  describe('getBusinessLikesReceived', () => {
    it('should fetch business likes received successfully', async () => {
      const mockUser = { id: 'user123' };
      const mockLikesData = {
        items: [
          {
            id: '1',
            user_id: 'customer1',
            profile_type: 'customer',
            customer_profiles: {
              id: 'customer1',
              name: 'John Doe',
              email: '<EMAIL>',
            },
          },
        ],
        totalCount: 1,
        hasMore: false,
        currentPage: 1,
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      jest.mocked(socialService.likesService.fetchBusinessLikesReceived).mockResolvedValue(mockLikesData);

      const result = await getBusinessLikesReceived({
        page: 1,
        pageSize: 10,
      });

      expect(result.likes).toEqual(mockLikesData.items);
      expect(result.count).toBe(mockLikesData.totalCount);
      expect(result.hasMore).toBe(mockLikesData.hasMore);
      expect(result.error).toBeNull();
      expect(socialService.likesService.fetchBusinessLikesReceived).toHaveBeenCalledWith(
        'user123',
        1,
        10
      );
    });

    it('should handle authentication error', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Not authenticated' },
      });

      const result = await getBusinessLikesReceived({
        page: 1,
        pageSize: 10,
      });

      expect(result.likes).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.hasMore).toBe(false);
      expect(result.error).toBe('Not authenticated');
    });

    it('should handle service error', async () => {
      const mockUser = { id: 'user123' };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      jest.mocked(socialService.likesService.fetchBusinessLikesReceived).mockRejectedValue(
        new Error('Service error')
      );

      const result = await getBusinessLikesReceived({
        page: 1,
        pageSize: 10,
      });

      expect(result.likes).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.hasMore).toBe(false);
      expect(result.error).toBe('Service error');
    });

    it('should handle missing user', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const result = await getBusinessLikesReceived({
        page: 1,
        pageSize: 10,
      });

      expect(result.likes).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.hasMore).toBe(false);
      expect(result.error).toBe('User not found');
    });
  });
});
