import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from "react-native";
import { X, Search } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createBusinessFollowersModalStyles } from "@/styles/modals/business/business-followers-modal";
import BusinessFollowersList from "./components/BusinessFollowersList";
import { FollowingList } from "../customer/components/FollowingList";

interface BusinessFollowersModalProps {
  visible: boolean;
  onClose: () => void;
  businessId: string;
}

export default function BusinessFollowersModal({
  visible,
  onClose,
  businessId,
}: BusinessFollowersModalProps) {
  const theme = useTheme();
  const styles = createBusinessFollowersModalStyles(theme);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeSearchTerm, setActiveSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"followers" | "following">("followers");

  const handleSearch = () => {
    setActiveSearchTerm(searchTerm);
  };

  const handleSearchSubmit = () => {
    handleSearch();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
          >
            <View style={styles.header}>
              <View style={{ width: 40 }} />
              <Text style={styles.headerTitle}>Followers</Text>
              <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                <X size={24} color={theme.colors.foreground} />
              </TouchableOpacity>
            </View>

            <View style={styles.contentContainer}>
              {/* Toggle Section */}
              <View style={styles.toggleContainer}>
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    activeTab === "followers"
                      ? styles.toggleButtonActive
                      : styles.toggleButtonInactive,
                  ]}
                  onPress={() => setActiveTab("followers")}
                >
                  <Text
                    style={[
                      styles.toggleButtonText,
                      activeTab === "followers"
                        ? styles.toggleButtonTextActive
                        : styles.toggleButtonTextInactive,
                    ]}
                  >
                    Followers
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    activeTab === "following"
                      ? styles.toggleButtonActive
                      : styles.toggleButtonInactive,
                  ]}
                  onPress={() => setActiveTab("following")}
                >
                  <Text
                    style={[
                      styles.toggleButtonText,
                      activeTab === "following"
                        ? styles.toggleButtonTextActive
                        : styles.toggleButtonTextInactive,
                    ]}
                  >
                    Following
                  </Text>
                </TouchableOpacity>
              </View>
              {/* Search Container */}
              <View style={styles.searchContainer}>
                <View style={styles.searchInputContainer}>
                  <Search
                    size={20}
                    color={theme.colors.mutedForeground}
                    style={styles.searchIcon}
                  />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search followers..."
                    placeholderTextColor={theme.colors.mutedForeground}
                    value={searchTerm}
                    onChangeText={setSearchTerm}
                    onSubmitEditing={handleSearchSubmit}
                    returnKeyType="search"
                  />
                </View>
              </View>

              {/* List Container */}
              <View style={styles.listContainer}>
                {activeTab === "followers" ? (
                  <BusinessFollowersList
                    businessId={businessId}
                    searchTerm={activeSearchTerm}
                  />
                ) : (
                  <FollowingList searchTerm={activeSearchTerm} />
                )}
              </View>
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </View>
    </Modal>
  );
}
